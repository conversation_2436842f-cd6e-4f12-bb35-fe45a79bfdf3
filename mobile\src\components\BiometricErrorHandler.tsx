import React from 'react';
import { Alert } from 'react-native';
import { BiometricErrorCodes } from '../types/biometric.types';
import { BIOMETRIC_CONSTANTS } from '../utils/biometric-constants';

interface BiometricErrorHandlerProps {
  errorCode: string;
  onRetry?: () => void;
  onFallback?: () => void;
  onSettings?: () => void;
  onReEnroll?: () => void;
}

export const BiometricErrorHandler: React.FC<BiometricErrorHandlerProps> = ({
  errorCode,
  onRetry,
  onFallback,
  onSettings,
  onReEnroll
}) => {
  
  const handleError = () => {
    switch (errorCode) {
      case BiometricErrorCodes.ERROR_LOCKOUT:
        Alert.alert(
          'Biometric Locked',
          BIOMETRIC_CONSTANTS.MESSAGES.LOCKOUT,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Use PIN/Password', onPress: onFallback }
          ]
        );
        break;

      case BiometricErrorCodes.ERROR_NO_BIOMETRICS:
        Alert.alert(
          'No Biometrics Enrolled',
          BIOMETRIC_CONSTANTS.MESSAGES.NO_BIOMETRICS,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: onSettings }
          ]
        );
        break;

      case BiometricErrorCodes.ERROR_HW_UNAVAILABLE:
        Alert.alert(
          'Hardware Unavailable',
          BIOMETRIC_CONSTANTS.MESSAGES.HW_UNAVAILABLE,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Try Again', onPress: onRetry }
          ]
        );
        break;

      case BiometricErrorCodes.ERROR_HW_NOT_PRESENT:
        Alert.alert(
          'Hardware Not Supported',
          BIOMETRIC_CONSTANTS.MESSAGES.HW_NOT_PRESENT,
          [{ text: 'OK' }]
        );
        break;

      case BiometricErrorCodes.ERROR_KEY_PERMANENTLY_INVALIDATED:
        Alert.alert(
          'Biometrics Changed',
          BIOMETRIC_CONSTANTS.MESSAGES.KEY_INVALIDATED,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Re-enroll', onPress: onReEnroll }
          ]
        );
        break;

      case BiometricErrorCodes.ERROR_TIMEOUT:
        Alert.alert(
          'Authentication Timeout',
          BIOMETRIC_CONSTANTS.MESSAGES.TIMEOUT,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Try Again', onPress: onRetry }
          ]
        );
        break;

      default:
        if (errorCode !== BiometricErrorCodes.ERROR_USER_CANCELED) {
          Alert.alert(
            'Authentication Failed',
            BIOMETRIC_CONSTANTS.MESSAGES.GENERIC_ERROR,
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Try Again', onPress: onRetry }
            ]
          );
        }
        break;
    }
  };

  React.useEffect(() => {
    if (errorCode) {
      handleError();
    }
  }, [errorCode]);

  return null; // This is a logic-only component
};
