{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,5880", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,5957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "6110", "endColumns": "100", "endOffsets": "6206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,141,211,294,364,439", "endColumns": "85,69,82,69,74,72", "endOffsets": "136,206,289,359,434,507"}, "to": {"startLines": "31,50,51,52,55,56", "startColumns": "4,4,4,4,4,4", "startOffsets": "3086,5532,5602,5685,5962,6037", "endColumns": "85,69,82,69,74,72", "endOffsets": "3167,5597,5680,5750,6032,6105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,345,460,615,705,873,1006,1121,1246,1375,1508,1644,1763,1902,1997,2159,2286,2435,2577,2705,2830,2931,3067,3175,3318,3420,3557", "endColumns": "160,128,114,154,89,167,132,114,124,128,132,135,118,138,94,161,126,148,141,127,124,100,135,107,142,101,136,102", "endOffsets": "211,340,455,610,700,868,1001,1116,1241,1370,1503,1639,1758,1897,1992,2154,2281,2430,2572,2700,2825,2926,3062,3170,3313,3415,3552,3655"}, "to": {"startLines": "29,30,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,53,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2796,2957,3172,3287,3442,3532,3700,3833,3948,4073,4202,4335,4471,4590,4729,4824,4986,5113,5262,5404,5755,6211,6312,6448,6556,6699,6801,6938", "endColumns": "160,128,114,154,89,167,132,114,124,128,132,135,118,138,94,161,126,148,141,127,124,100,135,107,142,101,136,102", "endOffsets": "2952,3081,3282,3437,3527,3695,3828,3943,4068,4197,4330,4466,4585,4724,4819,4981,5108,5257,5399,5527,5875,6307,6443,6551,6694,6796,6933,7036"}}]}]}