{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,218,302,374,441,508,585,650,715,787,859,936,1011,1077,1150,1224,1297,1374,1450,1522,1592,1661,1743,1811,1882,1949", "endColumns": "65,96,83,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "116,213,297,369,436,503,580,645,710,782,854,931,1006,1072,1145,1219,1292,1369,1445,1517,1587,1656,1738,1806,1877,1944,2016"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2685,2980,3077,3161,5185,5252,5319,5396,5461,5526,5598,5670,5747,5822,6004,6156,6230,6303,6380,6456,6528,6598,6667,6850,6918,6989,7056", "endColumns": "65,96,83,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "2746,3072,3156,3228,5247,5314,5391,5456,5521,5593,5665,5742,5817,5883,6072,6225,6298,6375,6451,6523,6593,6662,6744,6913,6984,7051,7123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,6077", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,6151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6749", "endColumns": "100", "endOffsets": "6845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,284,385,510,596,715,813,914,1018,1135,1243,1359,1465,1578,1672,1797,1899,2019,2133,2236,2352,2439,2550,2632,2735,2821,2931", "endColumns": "125,102,100,124,85,118,97,100,103,116,107,115,105,112,93,124,101,119,113,102,115,86,110,81,102,85,109,88", "endOffsets": "176,279,380,505,591,710,808,909,1013,1130,1238,1354,1460,1573,1667,1792,1894,2014,2128,2231,2347,2434,2545,2627,2730,2816,2926,3015"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2751,2877,3233,3334,3459,3545,3664,3762,3863,3967,4084,4192,4308,4414,4527,4621,4746,4848,4968,5082,5888,7128,7215,7326,7408,7511,7597,7707", "endColumns": "125,102,100,124,85,118,97,100,103,116,107,115,105,112,93,124,101,119,113,102,115,86,110,81,102,85,109,88", "endOffsets": "2872,2975,3329,3454,3540,3659,3757,3858,3962,4079,4187,4303,4409,4522,4616,4741,4843,4963,5077,5180,5999,7210,7321,7403,7506,7592,7702,7791"}}]}]}