{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6786", "endColumns": "100", "endOffsets": "6882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,284,386,523,608,728,825,926,1029,1142,1256,1375,1481,1597,1693,1821,1926,2045,2164,2272,2378,2464,2573,2651,2754,2840,2949", "endColumns": "125,102,101,136,84,119,96,100,102,112,113,118,105,115,95,127,104,118,118,107,105,85,108,77,102,85,108,88", "endOffsets": "176,279,381,518,603,723,820,921,1024,1137,1251,1370,1476,1592,1688,1816,1921,2040,2159,2267,2373,2459,2568,2646,2749,2835,2944,3033"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2759,2885,3240,3342,3479,3564,3684,3781,3882,3985,4098,4212,4331,4437,4553,4649,4777,4882,5001,5120,5937,7165,7251,7360,7438,7541,7627,7736", "endColumns": "125,102,101,136,84,119,96,100,102,112,113,118,105,115,95,127,104,118,118,107,105,85,108,77,102,85,108,88", "endOffsets": "2880,2983,3337,3474,3559,3679,3776,3877,3980,4093,4207,4326,4432,4548,4644,4772,4877,4996,5115,5223,6038,7246,7355,7433,7536,7622,7731,7820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,218,301,375,443,509,585,651,718,790,865,941,1017,1084,1159,1234,1306,1383,1459,1531,1601,1670,1748,1816,1887,1955", "endColumns": "67,94,82,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "118,213,296,370,438,504,580,646,713,785,860,936,1012,1079,1154,1229,1301,1378,1454,1526,1596,1665,1743,1811,1882,1950,2021"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2691,2988,3083,3166,5228,5296,5362,5438,5504,5571,5643,5718,5794,5870,6043,6197,6272,6344,6421,6497,6569,6639,6708,6887,6955,7026,7094", "endColumns": "67,94,82,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "2754,3078,3161,3235,5291,5357,5433,5499,5566,5638,5713,5789,5865,5932,6113,6267,6339,6416,6492,6564,6634,6703,6781,6950,7021,7089,7160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,6118", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,6192"}}]}]}