1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.autoflowmobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
12-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:4:5-74
12-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:4:22-71
13    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
13-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:5-72
13-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:22-69
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
14-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
15-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
16    <!--
17    This manifest file is used only by Gradle to configure debug-only capabilities
18    for React Native Apps.
19    -->
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:5-78
20-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:22-75
21
22    <permission
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:7:5-26:19
29        android:name="com.autoflowmobile.MainApplication"
29-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:8:7-38
30        android:allowBackup="false"
30-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:12:7-34
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
32        android:debuggable="true"
33        android:extractNativeLibs="true"
34        android:icon="@mipmap/ic_launcher"
34-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:7-41
35        android:label="@string/app_name"
35-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:7-39
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:11:7-52
37        android:theme="@style/AppTheme"
37-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:13:7-38
38        android:usesCleartextTraffic="true" >
38-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:6:9-44
39        <activity
39-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:14:7-25:18
40            android:name="com.autoflowmobile.MainActivity"
40-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:15:9-37
41            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
41-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:17:9-118
42            android:exported="true"
42-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:9-32
43            android:label="@string/app_name"
43-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:16:9-41
44            android:launchMode="singleTask"
44-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:18:9-40
45            android:windowSoftInputMode="adjustResize" >
45-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:19:9-51
46            <intent-filter>
46-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:9-24:25
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:22:13-65
47-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:22:21-62
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:23:13-73
49-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:23:23-70
50            </intent-filter>
51        </activity>
52        <activity
52-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:19:9-21:40
53            android:name="com.facebook.react.devsupport.DevSettingsActivity"
53-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:20:13-77
54            android:exported="false" />
54-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:21:13-37
55
56        <provider
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
57            android:name="androidx.startup.InitializationProvider"
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
58            android:authorities="com.autoflowmobile.androidx-startup"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
59            android:exported="false" >
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
60            <meta-data
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.emoji2.text.EmojiCompatInitializer"
61-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
62                android:value="androidx.startup" />
62-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
64-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
65                android:value="androidx.startup" />
65-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
68                android:value="androidx.startup" />
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
69        </provider>
70
71        <receiver
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
72            android:name="androidx.profileinstaller.ProfileInstallReceiver"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
73            android:directBootAware="false"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
74            android:enabled="true"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
75            android:exported="true"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
76            android:permission="android.permission.DUMP" >
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
78                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
81                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
84                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
87                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
88            </intent-filter>
89        </receiver>
90
91        <meta-data
91-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
92            android:name="com.facebook.soloader.enabled"
92-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
93            android:value="false" />
93-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
94    </application>
95
96</manifest>
