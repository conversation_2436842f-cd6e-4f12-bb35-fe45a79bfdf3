{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,348,461,607,698,851,971,1086,1210,1331,1460,1593,1719,1891,1997,2162,2296,2436,2578,2718,2843,2946,3080,3174,3299,3405,3542", "endColumns": "161,130,112,145,90,152,119,114,123,120,128,132,125,171,105,164,133,139,141,139,124,102,133,93,124,105,136,102", "endOffsets": "212,343,456,602,693,846,966,1081,1205,1326,1455,1588,1714,1886,1992,2157,2291,2431,2573,2713,2838,2941,3075,3169,3294,3400,3537,3640"}, "to": {"startLines": "29,30,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,54,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2848,3010,3220,3333,3479,3570,3723,3843,3958,4082,4203,4332,4465,4591,4763,4869,5034,5168,5308,5450,5873,6329,6432,6566,6660,6785,6891,7028", "endColumns": "161,130,112,145,90,152,119,114,123,120,128,132,125,171,105,164,133,139,141,139,124,102,133,93,124,105,136,102", "endOffsets": "3005,3136,3328,3474,3565,3718,3838,3953,4077,4198,4327,4460,4586,4758,4864,5029,5163,5303,5445,5585,5993,6427,6561,6655,6780,6886,7023,7126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,5998", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,6076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "6228", "endColumns": "100", "endOffsets": "6324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,134,201,281,348,417,491", "endColumns": "78,66,79,66,68,73,72", "endOffsets": "129,196,276,343,412,486,559"}, "to": {"startLines": "31,50,51,52,53,56,57", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3141,5590,5657,5737,5804,6081,6155", "endColumns": "78,66,79,66,68,73,72", "endOffsets": "3215,5652,5732,5799,5868,6150,6223"}}]}]}