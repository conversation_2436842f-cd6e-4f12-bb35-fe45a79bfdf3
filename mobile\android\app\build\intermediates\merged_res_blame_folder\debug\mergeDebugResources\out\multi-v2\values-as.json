{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,4747", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,4825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a26e82d508f491fb2ba1b9b8b2ee8b33\\transformed\\biometric-1.1.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,259,383,504,633,766,892,1064,1170,1310,1452", "endColumns": "112,90,123,120,128,132,125,171,105,139,141,139", "endOffsets": "163,254,378,499,628,761,887,1059,1165,1305,1447,1587"}, "to": {"startLines": "30,31,32,33,34,35,36,37,38,39,40,41", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2927,3040,3131,3255,3376,3505,3638,3764,3936,4042,4182,4324", "endColumns": "112,90,123,120,128,132,125,171,105,139,141,139", "endOffsets": "3035,3126,3250,3371,3500,3633,3759,3931,4037,4177,4319,4459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4977", "endColumns": "100", "endOffsets": "5073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,134,201,281,348,417,491", "endColumns": "78,66,79,66,68,73,72", "endOffsets": "129,196,276,343,412,486,559"}, "to": {"startLines": "29,42,43,44,45,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "2848,4464,4531,4611,4678,4830,4904", "endColumns": "78,66,79,66,68,73,72", "endOffsets": "2922,4526,4606,4673,4742,4899,4972"}}]}]}