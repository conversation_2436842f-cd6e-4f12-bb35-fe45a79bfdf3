import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY } from '../constants/theme';
import PartCard from '../components/PartCard';

type Screen = 'Home' | 'Shop' | 'Search' | 'Profile';

interface Props {
  onNavigate: (screen: Screen) => void;
}

// Mock parts data
const mockParts = [
  {
    id: 1,
    title: 'Volkswagen (VW) Audi Brake Pad Set 1K0698151 Compatible with Golf, A3, Passat',
    price: 'KSh 4,500',
    discountedPrice: 'KSh 5,200',
    condition: 'New',
    stock: 5,
    thumbnailUrl: null,
  },
  {
    id: 2,
    title: 'VW Golf Engine Oil Filter 06A115561B Compatible with Golf MK5, MK6',
    price: 'KSh 1,200',
    condition: 'New',
    stock: 12,
    thumbnailUrl: null,
  },
  {
    id: 3,
    title: 'Audi A3 Headlight Assembly 8P0941003 Compatible with A3 8P',
    price: 'KSh 15,000',
    condition: 'Used',
    stock: 2,
    thumbnailUrl: null,
  },
  {
    id: 4,
    title: 'VW Passat Timing Belt Kit 038198119A Compatible with Passat B5, B6',
    price: 'KSh 8,500',
    condition: 'New',
    stock: 8,
    thumbnailUrl: null,
  },
  {
    id: 5,
    title: 'Audi Q7 Air Suspension Compressor 7L8616007A',
    price: 'KSh 45,000',
    discountedPrice: 'KSh 52,000',
    condition: 'Refurbished',
    stock: 1,
    thumbnailUrl: null,
  },
];

const PartsScreen: React.FC<Props> = ({ onNavigate }) => {
  const handlePartPress = (partId: number) => {
    console.log('Part pressed:', partId);
    // TODO: Navigate to part details
  };

  const handleCallPress = (partId: number) => {
    console.log('Call pressed for part:', partId);
    // TODO: Implement call functionality
  };

  const handleWhatsAppPress = (partId: number) => {
    console.log('WhatsApp pressed for part:', partId);
    // TODO: Implement WhatsApp functionality
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Auto Parts</Text>
          <Text style={styles.subtitle}>Browse our extensive catalog</Text>
        </View>

        {/* Parts Grid */}
        <View style={styles.partsContainer}>
          {mockParts.map((part) => (
            <PartCard
              key={part.id}
              id={part.id}
              title={part.title}
              price={part.price}
              discountedPrice={part.discountedPrice}
              condition={part.condition}
              stock={part.stock}
              thumbnailUrl={part.thumbnailUrl}
              onPress={() => handlePartPress(part.id)}
              onCallPress={() => handleCallPress(part.id)}
              onWhatsAppPress={() => handleWhatsAppPress(part.id)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.lg,
    backgroundColor: COLORS.BACKGROUND,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_SECONDARY,
  },
  partsContainer: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.xl,
  },
});

export default PartsScreen;
