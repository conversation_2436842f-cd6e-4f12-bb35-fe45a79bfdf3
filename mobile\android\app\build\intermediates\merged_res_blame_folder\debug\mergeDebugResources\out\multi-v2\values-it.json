{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,5367", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,5444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6075", "endColumns": "100", "endOffsets": "6171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a26e82d508f491fb2ba1b9b8b2ee8b33\\transformed\\biometric-1.1.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,261,374,509,655,811,941,1099,1201,1338,1489", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "161,256,369,504,650,806,936,1094,1196,1333,1484,1609"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3154,3265,3360,3473,3608,3754,3910,4040,4198,4300,4437,4588", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "3260,3355,3468,3603,3749,3905,4035,4193,4295,4432,4583,4708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,233,326,410,481,553,641,721,805,895,976,1064,1150,1227,1307,1386,1461,1531,1600,1690,1765,1846", "endColumns": "69,107,92,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,228,321,405,476,548,636,716,800,890,971,1059,1145,1222,1302,1381,1456,1526,1595,1685,1760,1841,1928"}, "to": {"startLines": "29,30,31,32,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2799,2869,2977,3070,4713,4784,4856,4944,5024,5108,5198,5279,5449,5535,5612,5692,5771,5846,5916,5985,6176,6251,6332", "endColumns": "69,107,92,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "2864,2972,3065,3149,4779,4851,4939,5019,5103,5193,5274,5362,5530,5607,5687,5766,5841,5911,5980,6070,6246,6327,6414"}}]}]}