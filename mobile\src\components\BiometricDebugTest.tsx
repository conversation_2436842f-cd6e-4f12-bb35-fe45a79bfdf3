import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView
} from 'react-native';

// Direct import to test the library
import ReactNativeBiometrics from '@sbaiahmed1/react-native-biometrics';

const BiometricDebugTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    console.log('BiometricDebugTest:', message);
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testBasicLibrary = async () => {
    setIsLoading(true);
    addResult('Testing basic library import...');
    
    try {
      const rnBiometrics = new ReactNativeBiometrics({
        allowDeviceCredentials: true
      });
      addResult('✅ Library imported successfully');
      
      // Test isSensorAvailable
      const { available, biometryType, error } = await rnBiometrics.isSensorAvailable();
      addResult(`Sensor available: ${available}`);
      addResult(`Biometry type: ${biometryType || 'None'}`);
      if (error) {
        addResult(`Error: ${error}`);
      }
      
    } catch (error) {
      addResult(`❌ Library test failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testBiometricPrompt = async () => {
    setIsLoading(true);
    addResult('Testing biometric prompt...');
    
    try {
      const rnBiometrics = new ReactNativeBiometrics({
        allowDeviceCredentials: true
      });
      
      const { available } = await rnBiometrics.isSensorAvailable();
      if (!available) {
        addResult('❌ Biometric sensor not available');
        setIsLoading(false);
        return;
      }
      
      // Test simple prompt
      const { success, error } = await rnBiometrics.simplePrompt({
        promptMessage: 'Test biometric authentication',
        cancelButtonText: 'Cancel'
      });
      
      if (success) {
        addResult('✅ Biometric prompt successful!');
      } else {
        addResult(`❌ Biometric prompt failed: ${error}`);
      }
      
    } catch (error) {
      addResult(`❌ Prompt test failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testKeyGeneration = async () => {
    setIsLoading(true);
    addResult('Testing key generation...');
    
    try {
      const rnBiometrics = new ReactNativeBiometrics({
        allowDeviceCredentials: true
      });
      
      // Check if keys exist
      const { keysExist } = await rnBiometrics.biometricKeysExist();
      addResult(`Keys exist: ${keysExist}`);
      
      if (!keysExist) {
        // Generate keys
        const { publicKey } = await rnBiometrics.createKeys();
        addResult(`✅ Keys generated successfully`);
        addResult(`Public key length: ${publicKey?.length || 0}`);
      }
      
    } catch (error) {
      addResult(`❌ Key generation failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDeviceInfo = async () => {
    setIsLoading(true);
    addResult('Testing device info...');
    
    try {
      const rnBiometrics = new ReactNativeBiometrics({
        allowDeviceCredentials: true
      });
      
      const { available, biometryType, error } = await rnBiometrics.isSensorAvailable();
      
      addResult('=== DEVICE INFO ===');
      addResult(`Available: ${available}`);
      addResult(`Type: ${biometryType || 'Unknown'}`);
      addResult(`Error: ${error || 'None'}`);
      
      // Check keys
      const { keysExist } = await rnBiometrics.biometricKeysExist();
      addResult(`Keys exist: ${keysExist}`);
      
    } catch (error) {
      addResult(`❌ Device info failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Auto-run device info test on mount
    testDeviceInfo();
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔍 Biometric Debug Test</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton]} 
          onPress={testBasicLibrary}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Library</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]} 
          onPress={testBiometricPrompt}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Prompt</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]} 
          onPress={testKeyGeneration}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Keys</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.infoButton]} 
          onPress={testDeviceInfo}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Device Info</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>Clear</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
        {isLoading && (
          <Text style={styles.loadingText}>Running test...</Text>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    minWidth: '48%',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#34C759',
  },
  infoButton: {
    backgroundColor: '#FF9500',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    maxHeight: 400,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  resultText: {
    fontSize: 12,
    marginBottom: 5,
    color: '#666',
    fontFamily: 'monospace',
  },
  loadingText: {
    fontSize: 14,
    color: '#007AFF',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 10,
  },
});

export default BiometricDebugTest;
