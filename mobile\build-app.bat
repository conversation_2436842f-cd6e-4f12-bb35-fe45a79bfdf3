@echo off
echo ========================================
echo Building AutoFlow App with Biometric Authentication
echo ========================================

echo.
echo Step 1: Navigating to Android directory...
cd android

echo.
echo Step 2: Cleaning previous build...
call gradlew.bat clean

echo.
echo Step 3: Building debug APK...
call gradlew.bat assembleDebug

echo.
echo Step 4: Installing on connected device...
call gradlew.bat installDebug

echo.
echo Step 5: Launching app...
adb shell am start -n com.autoflowmobile/.MainActivity

echo.
echo ========================================
echo Build and Install Complete!
echo ========================================
echo.
echo The AutoFlow app with biometric authentication should now be running on your device.
echo.
echo To test the biometric features:
echo 1. Login with: <EMAIL> / password123
echo 2. Accept biometric setup when prompted
echo 3. Close and reopen app to test biometric login
echo.
echo If you encounter any issues, check the Metro bundler logs.
echo ========================================

pause
