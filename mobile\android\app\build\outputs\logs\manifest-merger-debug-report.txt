-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:1:1-32:12
MERGED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:1:1-32:12
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:flipper-integration:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\ee4ae5e64c61802912ca12749605ee0a\transformed\jetified-flipper-integration-0.73.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:sbaiahmed1_react-native-biometrics] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@sbaiahmed1\react-native-biometrics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-keychain] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\1c18d9cde7d715e713225bcd2026dd44\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3f26ab7fcd04c6c1eee011cf7d8f3f88\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0a49f7cc8d44f49027d421b3baff9e88\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3c97f369837365c09838ae0668e73b0b\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\4671ada73b10174dff6ecba084f7c23f\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\10d5304b4b3e56d84be9b37a4119b8b3\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\f544d71f8265de8250b9577dfcb95a2f\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\63c5a3baecfee20d894995870bc5efc6\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\649e2d24b4b762c7d37bd19fca507325\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\359891d2dfda3762e28d4921d78b4469\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\619916dabfcdb5ccd33b05da32196508\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\bef241a74fcf5be1e4c958337743f1f9\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\47e8e95717439522461c8b1379e856b5\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fc56619c9d5b6633168df9f0d35c87f9\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6dbfe87e9bded10f45e3b7ae0631fcf1\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\e92b667b0e2131a945c3069c32afa30c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\274989c52e5cb29f760e01c0c8d999e4\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d5312d584cb115585334d432ad2050\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:8:1-16:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7969a396d9c03415a53844e90e5afc4\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7ff490f0a0ebcee2b707af47db95ec36\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\52941fe7a2b4dfcee2149cc06fa0acbf\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ac726015019e861dce070d698b39c109\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.biometric:biometric:1.4.0-alpha04] C:\Users\<USER>\.gradle\caches\transforms-4\16801ecebf58528519bdc0bd327347a9\transformed\biometric-1.4.0-alpha04\AndroidManifest.xml:17:1-27:12
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f3cc09420e8ccfe99f443c7659aadc9\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec143e61bd8380e333d8603c60a81489\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\053935b21644a540e4de15f1d031a3a1\transformed\fragment-1.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\0f2cd041bea1a40788403227c22815a5\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c83e5efdb8d69442996ff6b9e38b7cf\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c8521c107aedde05766886b58cbb411\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd542cf04f0173883ff2e0ddfee08a56\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1bbe0baea93ada19fa51d7e8a5234d1\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da3e3798b330ef3e05862728f0b02188\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\436136d14e2610b2038ba345b6898d49\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6113285c0e78220de35ac04d6845ed59\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d39b91d1f2262675a3cf3bfea1f94ebf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\94285061969119739536e62248521246\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b1d3d828f48de9c2ad857eb4830aec4\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\79787ab02d9765c4e96f3f7ab2a29c49\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a59746a1b06db492d2e276387a08a7e4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\dd2f7999905de50514981e777b6bcac2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\df458c019b9b63d0ceef53ab6be68984\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\289faa5107c170ef2ec8f72a8c87cca4\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\35b6fd996d1aead515b76a42797dbefc\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\4c74eea83f6f83beab22cbcba5b3f2af\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e5c612c9ecaa5b20d63f8d33b2a9f29a\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\1010a7c217ecde968edb7bade6cd93d6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\801b9619c00ab4a19117e213f207994a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4355c535250bc31a3bccfed833d3398f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f36df648f494a6566845287080936d4\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dacacc0dfa591b0c4ebf620995986bf2\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b4f41ff6927fe4de7429da7c1c3fd82\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1892997b385bd5c7669531b7771676c\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3089f314f4da831e0e3e91b440305bd6\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b986e5c63e92aec8ea7fae66f8e62523\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\77b6ca47c6c3c15313b1047edee9fb29\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.react:hermes-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\837829eb1e26af0c93e67f49cb3c8db3\transformed\jetified-hermes-android-0.73.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c81e3d305b78a2b485d84341baedf907\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47905ba69a2335e856e1567b8d6ab224\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e2138332fd9b577f108442c465ad3e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27331e812418a7989ca19651231c3473\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d7db3777ac9373bfbede945aa86d14d\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ec2b669cc5803b46e9ee8e0445760b7\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be9085f12b4319e0db6c674e7faee927\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0768f5a9a36d9834ed51a1fbf7d8ff0\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\160f48f4c30f3ff757103849f5c02ea2\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3917bc0b21d7f3aacb77ceecea2e4e1\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\af6a0a791711645e4e2285242794cc35\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-4\84ce586532c36fe8746840074f16844d\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:1:1-3:12
	package
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.USE_FINGERPRINT
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:4:5-74
MERGED from [:react-native-keychain] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-74
MERGED from [:react-native-keychain] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-74
MERGED from [androidx.biometric:biometric:1.4.0-alpha04] C:\Users\<USER>\.gradle\caches\transforms-4\16801ecebf58528519bdc0bd327347a9\transformed\biometric-1.4.0-alpha04\AndroidManifest.xml:25:5-74
MERGED from [androidx.biometric:biometric:1.4.0-alpha04] C:\Users\<USER>\.gradle\caches\transforms-4\16801ecebf58528519bdc0bd327347a9\transformed\biometric-1.4.0-alpha04\AndroidManifest.xml:25:5-74
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:4:22-71
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:5-72
MERGED from [:react-native-keychain] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-72
MERGED from [:react-native-keychain] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-72
MERGED from [androidx.biometric:biometric:1.4.0-alpha04] C:\Users\<USER>\.gradle\caches\transforms-4\16801ecebf58528519bdc0bd327347a9\transformed\biometric-1.4.0-alpha04\AndroidManifest.xml:22:5-72
MERGED from [androidx.biometric:biometric:1.4.0-alpha04] C:\Users\<USER>\.gradle\caches\transforms-4\16801ecebf58528519bdc0bd327347a9\transformed\biometric-1.4.0-alpha04\AndroidManifest.xml:22:5-72
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:22-69
uses-permission#android.permission.USE_FACE_ID
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:6:5-70
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:6:22-67
uses-feature#android.hardware.fingerprint
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:5-90
	android:required
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:63-87
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:19-62
uses-feature#android.hardware.biometrics
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:5-89
	android:required
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:62-86
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:19-61
application
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:12:5-31:19
MERGED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:12:5-31:19
MERGED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:12:5-31:19
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:14:7-39
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:14:7-39
	tools:ignore
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:16:7-52
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:16:7-52
	tools:targetApi
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:15:7-41
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:15:7-41
	android:allowBackup
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:17:7-34
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:17:7-34
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:18:7-38
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:18:7-38
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:6:9-44
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:13:7-38
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:13:7-38
activity#com.autoflowmobile.MainActivity
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:19:7-30:18
	android:label
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:9-41
	android:launchMode
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:23:9-40
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:24:9-51
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:25:9-32
	android:configChanges
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:22:9-118
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:26:9-29:25
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:27:13-65
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:27:21-62
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:28:13-73
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:28:23-70
uses-sdk
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.react:flipper-integration:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\ee4ae5e64c61802912ca12749605ee0a\transformed\jetified-flipper-integration-0.73.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:flipper-integration:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\ee4ae5e64c61802912ca12749605ee0a\transformed\jetified-flipper-integration-0.73.2-debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sbaiahmed1_react-native-biometrics] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@sbaiahmed1\react-native-biometrics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sbaiahmed1_react-native-biometrics] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@sbaiahmed1\react-native-biometrics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-keychain] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:54
MERGED from [:react-native-keychain] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:54
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\1c18d9cde7d715e713225bcd2026dd44\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\1c18d9cde7d715e713225bcd2026dd44\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3f26ab7fcd04c6c1eee011cf7d8f3f88\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3f26ab7fcd04c6c1eee011cf7d8f3f88\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0a49f7cc8d44f49027d421b3baff9e88\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0a49f7cc8d44f49027d421b3baff9e88\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3c97f369837365c09838ae0668e73b0b\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3c97f369837365c09838ae0668e73b0b\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\4671ada73b10174dff6ecba084f7c23f\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\4671ada73b10174dff6ecba084f7c23f\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\10d5304b4b3e56d84be9b37a4119b8b3\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\10d5304b4b3e56d84be9b37a4119b8b3\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\f544d71f8265de8250b9577dfcb95a2f\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\f544d71f8265de8250b9577dfcb95a2f\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\63c5a3baecfee20d894995870bc5efc6\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\63c5a3baecfee20d894995870bc5efc6\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\649e2d24b4b762c7d37bd19fca507325\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\649e2d24b4b762c7d37bd19fca507325\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\359891d2dfda3762e28d4921d78b4469\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\359891d2dfda3762e28d4921d78b4469\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\619916dabfcdb5ccd33b05da32196508\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\619916dabfcdb5ccd33b05da32196508\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\bef241a74fcf5be1e4c958337743f1f9\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\bef241a74fcf5be1e4c958337743f1f9\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\47e8e95717439522461c8b1379e856b5\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\47e8e95717439522461c8b1379e856b5\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fc56619c9d5b6633168df9f0d35c87f9\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fc56619c9d5b6633168df9f0d35c87f9\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6dbfe87e9bded10f45e3b7ae0631fcf1\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6dbfe87e9bded10f45e3b7ae0631fcf1\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\e92b667b0e2131a945c3069c32afa30c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\e92b667b0e2131a945c3069c32afa30c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\274989c52e5cb29f760e01c0c8d999e4\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\274989c52e5cb29f760e01c0c8d999e4\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d5312d584cb115585334d432ad2050\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d5312d584cb115585334d432ad2050\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7969a396d9c03415a53844e90e5afc4\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7969a396d9c03415a53844e90e5afc4\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7ff490f0a0ebcee2b707af47db95ec36\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7ff490f0a0ebcee2b707af47db95ec36\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\52941fe7a2b4dfcee2149cc06fa0acbf\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\52941fe7a2b4dfcee2149cc06fa0acbf\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ac726015019e861dce070d698b39c109\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ac726015019e861dce070d698b39c109\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.biometric:biometric:1.4.0-alpha04] C:\Users\<USER>\.gradle\caches\transforms-4\16801ecebf58528519bdc0bd327347a9\transformed\biometric-1.4.0-alpha04\AndroidManifest.xml:20:5-44
MERGED from [androidx.biometric:biometric:1.4.0-alpha04] C:\Users\<USER>\.gradle\caches\transforms-4\16801ecebf58528519bdc0bd327347a9\transformed\biometric-1.4.0-alpha04\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f3cc09420e8ccfe99f443c7659aadc9\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f3cc09420e8ccfe99f443c7659aadc9\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec143e61bd8380e333d8603c60a81489\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec143e61bd8380e333d8603c60a81489\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\053935b21644a540e4de15f1d031a3a1\transformed\fragment-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\053935b21644a540e4de15f1d031a3a1\transformed\fragment-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\0f2cd041bea1a40788403227c22815a5\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\0f2cd041bea1a40788403227c22815a5\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c83e5efdb8d69442996ff6b9e38b7cf\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c83e5efdb8d69442996ff6b9e38b7cf\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c8521c107aedde05766886b58cbb411\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c8521c107aedde05766886b58cbb411\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd542cf04f0173883ff2e0ddfee08a56\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd542cf04f0173883ff2e0ddfee08a56\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1bbe0baea93ada19fa51d7e8a5234d1\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1bbe0baea93ada19fa51d7e8a5234d1\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da3e3798b330ef3e05862728f0b02188\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da3e3798b330ef3e05862728f0b02188\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\436136d14e2610b2038ba345b6898d49\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\436136d14e2610b2038ba345b6898d49\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6113285c0e78220de35ac04d6845ed59\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6113285c0e78220de35ac04d6845ed59\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d39b91d1f2262675a3cf3bfea1f94ebf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d39b91d1f2262675a3cf3bfea1f94ebf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\94285061969119739536e62248521246\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\94285061969119739536e62248521246\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b1d3d828f48de9c2ad857eb4830aec4\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b1d3d828f48de9c2ad857eb4830aec4\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\79787ab02d9765c4e96f3f7ab2a29c49\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\79787ab02d9765c4e96f3f7ab2a29c49\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a59746a1b06db492d2e276387a08a7e4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a59746a1b06db492d2e276387a08a7e4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\dd2f7999905de50514981e777b6bcac2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\dd2f7999905de50514981e777b6bcac2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\df458c019b9b63d0ceef53ab6be68984\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\df458c019b9b63d0ceef53ab6be68984\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\289faa5107c170ef2ec8f72a8c87cca4\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\289faa5107c170ef2ec8f72a8c87cca4\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\35b6fd996d1aead515b76a42797dbefc\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\35b6fd996d1aead515b76a42797dbefc\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\4c74eea83f6f83beab22cbcba5b3f2af\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\4c74eea83f6f83beab22cbcba5b3f2af\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e5c612c9ecaa5b20d63f8d33b2a9f29a\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e5c612c9ecaa5b20d63f8d33b2a9f29a\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\1010a7c217ecde968edb7bade6cd93d6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\1010a7c217ecde968edb7bade6cd93d6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\801b9619c00ab4a19117e213f207994a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\801b9619c00ab4a19117e213f207994a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4355c535250bc31a3bccfed833d3398f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4355c535250bc31a3bccfed833d3398f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f36df648f494a6566845287080936d4\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f36df648f494a6566845287080936d4\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dacacc0dfa591b0c4ebf620995986bf2\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dacacc0dfa591b0c4ebf620995986bf2\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b4f41ff6927fe4de7429da7c1c3fd82\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b4f41ff6927fe4de7429da7c1c3fd82\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1892997b385bd5c7669531b7771676c\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1892997b385bd5c7669531b7771676c\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3089f314f4da831e0e3e91b440305bd6\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3089f314f4da831e0e3e91b440305bd6\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b986e5c63e92aec8ea7fae66f8e62523\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b986e5c63e92aec8ea7fae66f8e62523\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\77b6ca47c6c3c15313b1047edee9fb29\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\77b6ca47c6c3c15313b1047edee9fb29\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.react:hermes-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\837829eb1e26af0c93e67f49cb3c8db3\transformed\jetified-hermes-android-0.73.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\837829eb1e26af0c93e67f49cb3c8db3\transformed\jetified-hermes-android-0.73.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c81e3d305b78a2b485d84341baedf907\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c81e3d305b78a2b485d84341baedf907\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47905ba69a2335e856e1567b8d6ab224\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47905ba69a2335e856e1567b8d6ab224\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e2138332fd9b577f108442c465ad3e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e2138332fd9b577f108442c465ad3e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27331e812418a7989ca19651231c3473\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27331e812418a7989ca19651231c3473\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d7db3777ac9373bfbede945aa86d14d\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d7db3777ac9373bfbede945aa86d14d\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ec2b669cc5803b46e9ee8e0445760b7\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ec2b669cc5803b46e9ee8e0445760b7\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be9085f12b4319e0db6c674e7faee927\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be9085f12b4319e0db6c674e7faee927\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0768f5a9a36d9834ed51a1fbf7d8ff0\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0768f5a9a36d9834ed51a1fbf7d8ff0\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\160f48f4c30f3ff757103849f5c02ea2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\160f48f4c30f3ff757103849f5c02ea2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3917bc0b21d7f3aacb77ceecea2e4e1\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3917bc0b21d7f3aacb77ceecea2e4e1\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\af6a0a791711645e4e2285242794cc35\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\af6a0a791711645e4e2285242794cc35\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-4\84ce586532c36fe8746840074f16844d\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-4\84ce586532c36fe8746840074f16844d\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
	tools:overrideLibrary
		ADDED from [:react-native-keychain] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-51
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
	android:name
		ADDED from [:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:22-75
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:20:13-77
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a23cb9dff2d510842e5d4415eec67e3\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
