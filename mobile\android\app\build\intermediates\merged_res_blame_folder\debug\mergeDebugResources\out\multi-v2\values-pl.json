{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6032", "endColumns": "100", "endOffsets": "6128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a26e82d508f491fb2ba1b9b8b2ee8b33\\transformed\\biometric-1.1.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,249,374,512,665,792,920,1067,1167,1301,1440", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "154,244,369,507,660,787,915,1062,1162,1296,1435,1559"}, "to": {"startLines": "32,33,34,35,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3093,3197,3287,3412,3550,3703,3830,3958,4105,4205,4339,4478", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "3192,3282,3407,3545,3698,3825,3953,4100,4200,4334,4473,4597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,254,331,404,473,558,634,713,796,891,961,1046,1132,1207,1289,1372,1450,1522,1592,1678,1756,1832,1906", "endColumns": "105,92,76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "156,249,326,399,468,553,629,708,791,886,956,1041,1127,1202,1284,1367,1445,1517,1587,1673,1751,1827,1901,1981"}, "to": {"startLines": "29,30,31,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2817,2923,3016,4602,4675,4744,4829,4905,4984,5067,5162,5232,5400,5486,5561,5643,5726,5804,5876,5946,6133,6211,6287,6361", "endColumns": "105,92,76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "2918,3011,3088,4670,4739,4824,4900,4979,5062,5157,5227,5312,5481,5556,5638,5721,5799,5871,5941,6027,6206,6282,6356,6436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,5317", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,5395"}}]}]}