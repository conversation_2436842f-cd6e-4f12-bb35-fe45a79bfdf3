{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "7312", "endColumns": "100", "endOffsets": "7408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,6592", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,6667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,216,293,364,434,517,588,655,732,812,897,977,1047,1130,1215,1290,1375,1461,1538,1612,1683,1770,1840,1919,1994", "endColumns": "68,91,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "119,211,288,359,429,512,583,650,727,807,892,972,1042,1125,1210,1285,1370,1456,1533,1607,1678,1765,1835,1914,1989,2066"}, "to": {"startLines": "29,32,33,52,53,54,55,56,57,58,59,60,61,63,65,66,67,68,69,70,71,72,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,3177,3269,5630,5701,5771,5854,5925,5992,6069,6149,6234,6314,6509,6672,6757,6832,6917,7003,7080,7154,7225,7413,7483,7562,7637", "endColumns": "68,91,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "2861,3264,3341,5696,5766,5849,5920,5987,6064,6144,6229,6309,6379,6587,6752,6827,6912,6998,7075,7149,7220,7307,7478,7557,7632,7709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,366,472,627,719,864,977,1090,1209,1338,1463,1607,1724,1867,1962,2121,2247,2371,2512,2650,2775,2870,2998,3089,3213,3311,3442", "endColumns": "173,136,105,154,91,144,112,112,118,128,124,143,116,142,94,158,125,123,140,137,124,94,127,90,123,97,130,99", "endOffsets": "224,361,467,622,714,859,972,1085,1204,1333,1458,1602,1719,1862,1957,2116,2242,2366,2507,2645,2770,2865,2993,3084,3208,3306,3437,3537"}, "to": {"startLines": "30,31,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,62,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2866,3040,3346,3452,3607,3699,3844,3957,4070,4189,4318,4443,4587,4704,4847,4942,5101,5227,5351,5492,6384,7714,7809,7937,8028,8152,8250,8381", "endColumns": "173,136,105,154,91,144,112,112,118,128,124,143,116,142,94,158,125,123,140,137,124,94,127,90,123,97,130,99", "endOffsets": "3035,3172,3447,3602,3694,3839,3952,4065,4184,4313,4438,4582,4699,4842,4937,5096,5222,5346,5487,5625,6504,7804,7932,8023,8147,8245,8376,8476"}}]}]}