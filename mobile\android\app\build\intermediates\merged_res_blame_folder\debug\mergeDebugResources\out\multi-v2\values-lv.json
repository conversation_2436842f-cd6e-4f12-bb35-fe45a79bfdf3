{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a26e82d508f491fb2ba1b9b8b2ee8b33\\transformed\\biometric-1.1.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,255,387,523,657,796,931,1066,1164,1300,1462", "endColumns": "108,90,131,135,133,138,134,134,97,135,161,119", "endOffsets": "159,250,382,518,652,791,926,1061,1159,1295,1457,1577"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3352,3461,3552,3684,3820,3954,4093,4228,4363,4461,4597,4759", "endColumns": "108,90,131,135,133,138,134,134,97,135,161,119", "endOffsets": "3456,3547,3679,3815,3949,4088,4223,4358,4456,4592,4754,4874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "6445", "endColumns": "100", "endOffsets": "6541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,244,332,418,493,563,645,713,783,862,944,1028,1111,1181,1266,1347,1424,1506,1587,1663,1739,1814,1901,1979,2059,2131", "endColumns": "73,114,87,85,74,69,81,67,69,78,81,83,82,69,84,80,76,81,80,75,75,74,86,77,79,71,73", "endOffsets": "124,239,327,413,488,558,640,708,778,857,939,1023,1106,1176,1261,1342,1419,1501,1582,1658,1734,1809,1896,1974,2054,2126,2200"}, "to": {"startLines": "29,30,31,32,45,46,47,48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2989,3063,3178,3266,4879,4954,5024,5106,5174,5244,5323,5405,5489,5572,5642,5810,5891,5968,6050,6131,6207,6283,6358,6546,6624,6704,6776", "endColumns": "73,114,87,85,74,69,81,67,69,78,81,83,82,69,84,80,76,81,80,75,75,74,86,77,79,71,73", "endOffsets": "3058,3173,3261,3347,4949,5019,5101,5169,5239,5318,5400,5484,5567,5637,5722,5886,5963,6045,6126,6202,6278,6353,6440,6619,6699,6771,6845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,5727", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,5805"}}]}]}