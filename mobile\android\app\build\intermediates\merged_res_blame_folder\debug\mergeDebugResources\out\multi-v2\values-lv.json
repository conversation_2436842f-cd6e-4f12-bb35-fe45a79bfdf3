{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7766", "endColumns": "100", "endOffsets": "7862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,244,332,418,493,563,645,713,783,862,944,1028,1111,1181,1266,1347,1424,1506,1587,1663,1739,1814,1901,1979,2059,2131", "endColumns": "73,114,87,85,74,69,81,67,69,78,81,83,82,69,84,80,76,81,80,75,75,74,86,77,79,71,73", "endOffsets": "124,239,327,413,488,558,640,708,778,857,939,1023,1106,1176,1261,1342,1419,1501,1582,1658,1734,1809,1896,1974,2054,2126,2200"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2989,3363,3478,3566,6061,6136,6206,6288,6356,6426,6505,6587,6671,6754,6963,7131,7212,7289,7371,7452,7528,7604,7679,7867,7945,8025,8097", "endColumns": "73,114,87,85,74,69,81,67,69,78,81,83,82,69,84,80,76,81,80,75,75,74,86,77,79,71,73", "endOffsets": "3058,3473,3561,3647,6131,6201,6283,6351,6421,6500,6582,6666,6749,6819,7043,7207,7284,7366,7447,7523,7599,7674,7761,7940,8020,8092,8166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,355,464,621,712,884,1012,1131,1263,1399,1533,1672,1807,1942,2040,2215,2346,2482,2644,2764,2903,3008,3157,3262,3411,3519,3671", "endColumns": "171,127,108,156,90,171,127,118,131,135,133,138,134,134,97,174,130,135,161,119,138,104,148,104,148,107,151,113", "endOffsets": "222,350,459,616,707,879,1007,1126,1258,1394,1528,1667,1802,1937,2035,2210,2341,2477,2639,2759,2898,3003,3152,3257,3406,3514,3666,3780"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3063,3235,3652,3761,3918,4009,4181,4309,4428,4560,4696,4830,4969,5104,5239,5337,5512,5643,5779,5941,6824,8171,8276,8425,8530,8679,8787,8939", "endColumns": "171,127,108,156,90,171,127,118,131,135,133,138,134,134,97,174,130,135,161,119,138,104,148,104,148,107,151,113", "endOffsets": "3230,3358,3756,3913,4004,4176,4304,4423,4555,4691,4825,4964,5099,5234,5332,5507,5638,5774,5936,6056,6958,8271,8420,8525,8674,8782,8934,9048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,7048", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,7126"}}]}]}