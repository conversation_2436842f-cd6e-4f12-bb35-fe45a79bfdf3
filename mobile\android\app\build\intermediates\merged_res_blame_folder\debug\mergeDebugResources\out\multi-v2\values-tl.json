{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,5990", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,6070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,341,455,615,711,861,979,1093,1215,1368,1511,1663,1790,1928,2028,2191,2322,2459,2611,2737,2870,2970,3102,3192,3314,3417,3552", "endColumns": "158,126,113,159,95,149,117,113,121,152,142,151,126,137,99,162,130,136,151,125,132,99,131,89,121,102,134,105", "endOffsets": "209,336,450,610,706,856,974,1088,1210,1363,1506,1658,1785,1923,2023,2186,2317,2454,2606,2732,2865,2965,3097,3187,3309,3412,3547,3653"}, "to": {"startLines": "29,30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,53,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2845,3004,3299,3413,3573,3669,3819,3937,4051,4173,4326,4469,4621,4748,4886,4986,5149,5280,5417,5569,5857,6583,6683,6815,6905,7027,7130,7265", "endColumns": "158,126,113,159,95,149,117,113,121,152,142,151,126,137,99,162,130,136,151,125,132,99,131,89,121,102,134,105", "endOffsets": "2999,3126,3408,3568,3664,3814,3932,4046,4168,4321,4464,4616,4743,4881,4981,5144,5275,5412,5564,5690,5985,6678,6810,6900,7022,7125,7260,7366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "6482", "endColumns": "100", "endOffsets": "6578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,223,302,385,472,556,632,706", "endColumns": "90,76,78,82,86,83,75,73,85", "endOffsets": "141,218,297,380,467,551,627,701,787"}, "to": {"startLines": "31,32,51,52,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3131,3222,5695,5774,6075,6162,6246,6322,6396", "endColumns": "90,76,78,82,86,83,75,73,85", "endOffsets": "3217,3294,5769,5852,6157,6241,6317,6391,6477"}}]}]}