{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,203,321,426,579,666,803,918,1031,1153,1282,1414,1562,1689,1842,1941,2091,2211,2351,2488,2618,2741,2836,2961,3053,3167,3264,3391", "endColumns": "147,117,104,152,86,136,114,112,121,128,131,147,126,152,98,149,119,139,136,129,122,94,124,91,113,96,126,95", "endOffsets": "198,316,421,574,661,798,913,1026,1148,1277,1409,1557,1684,1837,1936,2086,2206,2346,2483,2613,2736,2831,2956,3048,3162,3259,3386,3482"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2838,2986,3385,3490,3643,3730,3867,3982,4095,4217,4346,4478,4626,4753,4906,5005,5155,5275,5415,5552,6416,7715,7810,7935,8027,8141,8238,8365", "endColumns": "147,117,104,152,86,136,114,112,121,128,131,147,126,152,98,149,119,139,136,129,122,94,124,91,113,96,126,95", "endOffsets": "2981,3099,3485,3638,3725,3862,3977,4090,4212,4341,4473,4621,4748,4901,5000,5150,5270,5410,5547,5677,6534,7805,7930,8022,8136,8233,8360,8456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,239,330,407,478,547,627,694,761,835,911,994,1073,1141,1219,1302,1376,1460,1548,1623,1694,1765,1851,1920,1994,2063", "endColumns": "70,112,90,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "121,234,325,402,473,542,622,689,756,830,906,989,1068,1136,1214,1297,1371,1455,1543,1618,1689,1760,1846,1915,1989,2058,2131"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2767,3104,3217,3308,5682,5753,5822,5902,5969,6036,6110,6186,6269,6348,6539,6697,6780,6854,6938,7026,7101,7172,7243,7430,7499,7573,7642", "endColumns": "70,112,90,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "2833,3212,3303,3380,5748,5817,5897,5964,6031,6105,6181,6264,6343,6411,6612,6775,6849,6933,7021,7096,7167,7238,7324,7494,7568,7637,7710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,6617", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,6692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7329", "endColumns": "100", "endOffsets": "7425"}}]}]}