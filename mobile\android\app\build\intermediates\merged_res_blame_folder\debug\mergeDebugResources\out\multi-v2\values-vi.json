{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "72", "startColumns": "4", "startOffsets": "7240", "endColumns": "100", "endOffsets": "7336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,234,323,400,472,544,627,698,774,855,938,1021,1099,1176,1248,1332,1415,1492,1568,1643,1733,1806,1885,1959", "endColumns": "72,105,88,76,71,71,82,70,75,80,82,82,77,76,71,83,82,76,75,74,89,72,78,73,78", "endOffsets": "123,229,318,395,467,539,622,693,769,850,933,1016,1094,1171,1243,1327,1410,1487,1563,1638,1728,1801,1880,1954,2033"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,62,64,65,66,67,68,69,70,71,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2798,3161,3267,3356,5694,5766,5838,5921,5992,6068,6149,6232,6443,6606,6683,6755,6839,6922,6999,7075,7150,7341,7414,7493,7567", "endColumns": "72,105,88,76,71,71,82,70,75,80,82,82,77,76,71,83,82,76,75,74,89,72,78,73,78", "endOffsets": "2866,3262,3351,3428,5761,5833,5916,5987,6063,6144,6227,6310,6516,6678,6750,6834,6917,6994,7070,7145,7235,7409,7488,7562,7641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,345,452,608,703,854,959,1072,1188,1316,1446,1578,1692,1827,1933,2089,2199,2337,2483,2606,2734,2839,2990,3078,3212,3305,3444", "endColumns": "167,121,106,155,94,150,104,112,115,127,129,131,113,134,105,155,109,137,145,122,127,104,150,87,133,92,138,110", "endOffsets": "218,340,447,603,698,849,954,1067,1183,1311,1441,1573,1687,1822,1928,2084,2194,2332,2478,2601,2729,2834,2985,3073,3207,3300,3439,3550"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,61,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2871,3039,3433,3540,3696,3791,3942,4047,4160,4276,4404,4534,4666,4780,4915,5021,5177,5287,5425,5571,6315,7646,7751,7902,7990,8124,8217,8356", "endColumns": "167,121,106,155,94,150,104,112,115,127,129,131,113,134,105,155,109,137,145,122,127,104,150,87,133,92,138,110", "endOffsets": "3034,3156,3535,3691,3786,3937,4042,4155,4271,4399,4529,4661,4775,4910,5016,5172,5282,5420,5566,5689,6438,7746,7897,7985,8119,8212,8351,8462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,6521", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,6601"}}]}]}