// Determine if we're in development mode
const isDevelopment = __DEV__;

export const CONFIG = {
  // App Info
  APP_NAME: 'AutoFlow',
  APP_VERSION: '1.0.0',

  // API Configuration - Update these with your actual API URLs
  API_BASE_URL: isDevelopment
    ? 'http://localhost:3000' // Development API URL
    : 'https://your-production-api.com', // Production API URL

  // Supabase Configuration - Update with your actual Supabase credentials
  SUPABASE: {
    URL: 'https://your-supabase-url.supabase.co',
    ANON_KEY: 'your-supabase-anon-key',
  },

  // M-PESA Configuration - Update with your actual M-PESA credentials
  MPESA: {
    CONSUMER_KEY: 'your-mpesa-consumer-key',
    CONSUMER_SECRET: 'your-mpesa-consumer-secret',
    SHORT_CODE: 'your-mpesa-shortcode',
    ENVIRONMENT: 'sandbox',
    IS_SANDBOX: true,
  },
  
  // API Endpoints
  ENDPOINTS: {
    // Authentication
    AUTH: {
      LOGIN: '/api/auth/login',
      LOGOUT: '/api/auth/logout',
      REGISTER: '/api/auth/register',
      OTP: '/api/auth/otp',
      FORGOT_PASSWORD: '/api/auth/forgot-password',
      RESET_PASSWORD: '/api/auth/reset-password',
    },
    
    // Parts
    PARTS: {
      LIST: '/api/parts',
      SEARCH: '/api/parts/search',
      DETAILS: (id: number) => `/api/parts/${id}`,
      ADD: '/api/parts/add',
      UPDATE: (id: number) => `/api/parts/update/${id}`,
      DELETE: (id: number) => `/api/parts/delete/${id}`,
      CATEGORIES: '/api/parts/categories',
      FILTER_BY_CAR: '/api/parts/filter-by-car',
    },
    
    // Cars
    CARS: {
      ALL: '/api/car/all',
      BRANDS: '/api/car/brands',
      MODELS: '/api/car/models',
      GENERATIONS: '/api/car/generations',
      VARIATIONS: '/api/car/variations',
      TRIMS: '/api/car/trims',
    },
    
    // Sales
    SALES: {
      LIST: '/api/sales',
      CREATE: '/api/sales',
      DETAILS: (id: string) => `/api/sales/${id}`,
      UPDATE: (id: string) => `/api/sales/${id}`,
      DELETE: (id: string) => `/api/sales/${id}`,
    },
    
    // Clients
    CLIENTS: {
      LIST: '/api/clients',
      CREATE: '/api/clients',
      DETAILS: (id: string) => `/api/clients/${id}`,
      UPDATE: (id: string) => `/api/clients/${id}`,
      DELETE: (id: string) => `/api/clients/${id}`,
    },
    
    // M-PESA
    MPESA: {
      INITIATE: '/api/mpesa/initiate',
      QUERY: '/api/mpesa/query',
      CALLBACK: '/api/mpesa/callback',
    },
    
    // Admin
    ADMIN: {
      USERS: '/api/admin/users',
      ROLES: '/api/admin/roles',
      PERMISSIONS: '/api/admin/permissions',
    },
    
    // RBAC
    RBAC: {
      CHECK_PERMISSION: '/api/rbac/check-permission',
      SETUP: '/api/rbac/setup',
    },
  },
  
  // App Settings
  SETTINGS: {
    DEFAULT_PAGE_SIZE: 12,
    MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
    SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
    CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
    REQUEST_TIMEOUT: 30000, // 30 seconds
  },
  
  // Theme Colors
  COLORS: {
    PRIMARY: '#14b8a6',
    SECONDARY: '#0f766e',
    SUCCESS: '#10b981',
    WARNING: '#f59e0b',
    ERROR: '#ef4444',
    INFO: '#3b82f6',
    BACKGROUND: '#ffffff',
    SURFACE: '#f8fafc',
    TEXT_PRIMARY: '#1f2937',
    TEXT_SECONDARY: '#6b7280',
    BORDER: '#e5e7eb',
  },
  
  // Storage Keys
  STORAGE_KEYS: {
    USER_TOKEN: 'user_token',
    USER_PROFILE: 'user_profile',
    SELECTED_CAR: 'selected_car',
    SEARCH_HISTORY: 'search_history',
    CART_ITEMS: 'cart_items',
    FAVORITES: 'favorites',
    SETTINGS: 'app_settings',
  },
};

export default CONFIG;
