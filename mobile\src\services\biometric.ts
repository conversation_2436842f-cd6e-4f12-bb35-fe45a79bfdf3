import ReactNativeBiometrics, { BiometryTypes } from 'react-native-biometrics';
import * as Keychain from 'react-native-keychain';
import { simpleStorage } from './storage';

export interface BiometricConfig {
  title: string;
  subtitle?: string;
  description?: string;
  fallbackLabel?: string;
  cancelLabel?: string;
  color?: string;
}

export interface BiometricCredentials {
  email: string;
  userId: string;
}

export class BiometricService {
  private static readonly BIOMETRIC_KEY = 'autoflow_biometric_credentials';
  private static readonly BIOMETRIC_ENABLED_KEY = 'autoflow_biometric_enabled';
  private rnBiometrics = new ReactNativeBiometrics();

  // Check if biometric authentication is available
  async isBiometricAvailable(): Promise<boolean> {
    try {
      console.log('BiometricService: Checking biometric availability...');

      const { available, biometryType, error } = await this.rnBiometrics.isSensorAvailable();
      console.log('BiometricService: Sensor available:', available);
      console.log('BiometricService: Biometry type:', biometryType);
      console.log('BiometricService: Error (if any):', error);

      if (error) {
        console.log('BiometricService: Sensor error details:', error);
      }

      return available;
    } catch (error) {
      console.log('BiometricService: Error checking availability:', error);
      return false;
    }
  }

  // Get supported biometric type
  async getBiometricType(): Promise<string | null> {
    try {
      console.log('BiometricService: Getting biometric type...');

      const { available, biometryType } = await this.rnBiometrics.isSensorAvailable();

      if (available && biometryType) {
        console.log('BiometricService: Biometry type:', biometryType);

        // Convert react-native-biometrics types to user-friendly names
        switch (biometryType) {
          case BiometryTypes.TouchID:
            return 'Touch ID';
          case BiometryTypes.FaceID:
            return 'Face ID';
          case BiometryTypes.Biometrics:
            return 'Fingerprint';
          default:
            return 'Biometric';
        }
      }

      return null;
    } catch (error) {
      console.log('BiometricService: Error getting biometric type:', error);
      return null;
    }
  }

  // Check if user has enabled biometric authentication
  async isBiometricEnabled(): Promise<boolean> {
    try {
      const enabled = await simpleStorage.getItem(BiometricService.BIOMETRIC_ENABLED_KEY);
      return enabled === 'true';
    } catch (error) {
      console.log('Error checking biometric enabled status:', error);
      return false;
    }
  }

  // Enable biometric authentication
  async enableBiometric(email: string, userId: string): Promise<boolean> {
    try {
      console.log('BiometricService: Enabling biometric authentication for', email);

      // Check if biometric authentication is available
      const isAvailable = await this.isBiometricAvailable();
      if (!isAvailable) {
        throw new Error('Biometric authentication is not available on this device');
      }

      console.log('BiometricService: Prompting for biometric authentication...');

      // Show biometric prompt to verify user can authenticate
      const result = await this.rnBiometrics.simplePrompt({
        promptMessage: 'Place your finger on the sensor to enable biometric login',
        cancelButtonText: 'Cancel',
      });

      console.log('BiometricService: Prompt result:', result);

      if (!result.success) {
        const errorMsg = result.error || 'Biometric authentication failed';
        console.log('BiometricService: Authentication failed:', errorMsg);
        throw new Error(errorMsg);
      }

      console.log('BiometricService: Biometric authentication successful, storing credentials...');

      // Store credentials securely
      const credentials: BiometricCredentials = {
        email,
        userId,
      };

      // Store in Keychain with biometric protection
      await Keychain.setInternetCredentials(
        BiometricService.BIOMETRIC_KEY,
        email,
        JSON.stringify(credentials),
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY,
          authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
        }
      );

      // Mark biometric as enabled
      await simpleStorage.setItem(BiometricService.BIOMETRIC_ENABLED_KEY, 'true');

      console.log('BiometricService: Biometric authentication enabled successfully');
      return true;
    } catch (error) {
      console.log('BiometricService: Error enabling biometric:', error);
      throw error;
    }
  }

  // Disable biometric authentication
  async disableBiometric(): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(BiometricService.BIOMETRIC_KEY);
      await simpleStorage.setItem(BiometricService.BIOMETRIC_ENABLED_KEY, 'false');
    } catch (error) {
      console.log('Error disabling biometric:', error);
      throw error;
    }
  }

  // This method is no longer needed as Keychain handles authentication internally

  // Get stored credentials after biometric authentication
  async getBiometricCredentials(): Promise<BiometricCredentials | null> {
    try {
      console.log('BiometricService: Authenticating with biometrics...');

      // Show biometric prompt for authentication
      const result = await this.rnBiometrics.simplePrompt({
        promptMessage: 'Use your fingerprint to login to AutoFlow',
        cancelButtonText: 'Cancel',
      });

      console.log('BiometricService: Authentication result:', result);

      if (!result.success) {
        const errorMsg = result.error || 'Biometric authentication cancelled or failed';
        console.log('BiometricService: Authentication failed:', errorMsg);
        throw new Error(errorMsg);
      }

      console.log('BiometricService: Biometric authentication successful, retrieving credentials...');

      // Get stored credentials from Keychain
      const credentials = await Keychain.getInternetCredentials(BiometricService.BIOMETRIC_KEY);

      if (credentials && credentials.password) {
        console.log('BiometricService: Credentials retrieved successfully');
        return JSON.parse(credentials.password) as BiometricCredentials;
      }

      console.log('BiometricService: No credentials found');
      return null;
    } catch (error) {
      console.log('BiometricService: Error getting biometric credentials:', error);
      throw error;
    }
  }



  // Check if biometric credentials exist
  async hasBiometricCredentials(): Promise<boolean> {
    try {
      console.log('BiometricService: Checking if biometric credentials exist');

      const credentials = await Keychain.getInternetCredentials(BiometricService.BIOMETRIC_KEY);
      const hasCredentials = credentials && credentials.password !== false;

      console.log('BiometricService: Has biometric credentials:', hasCredentials);
      return hasCredentials;
    } catch (error) {
      console.log('BiometricService: Error checking biometric credentials:', error);
      return false;
    }
  }

  // Get biometric prompt message based on type
  async getBiometricPromptMessage(): Promise<string> {
    const biometricType = await this.getBiometricType();
    switch (biometricType) {
      case 'Face ID':
        return 'Use Face ID to login to AutoFlow';
      case 'Touch ID':
        return 'Use Touch ID to login to AutoFlow';
      case 'Fingerprint':
        return 'Use your fingerprint to login to AutoFlow';
      default:
        return 'Use biometric authentication to login to AutoFlow';
    }
  }
}

export const biometricService = new BiometricService();
export default biometricService;
