{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "31,50,51,52,53,56,57", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3174,5640,5708,5787,5855,6143,6213", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "3246,5703,5782,5850,5917,6208,6277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,364,482,633,723,879,1006,1125,1247,1375,1507,1652,1784,1932,2028,2200,2343,2483,2622,2753,2894,3014,3163,3268,3402,3523,3673", "endColumns": "168,139,117,150,89,155,126,118,121,127,131,144,131,147,95,171,142,139,138,130,140,119,148,104,133,120,149,118", "endOffsets": "219,359,477,628,718,874,1001,1120,1242,1370,1502,1647,1779,1927,2023,2195,2338,2478,2617,2748,2889,3009,3158,3263,3397,3518,3668,3787"}, "to": {"startLines": "29,30,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,54,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2865,3034,3251,3369,3520,3610,3766,3893,4012,4134,4262,4394,4539,4671,4819,4915,5087,5230,5370,5509,5922,6383,6503,6652,6757,6891,7012,7162", "endColumns": "168,139,117,150,89,155,126,118,121,127,131,144,131,147,95,171,142,139,138,130,140,119,148,104,133,120,149,118", "endOffsets": "3029,3169,3364,3515,3605,3761,3888,4007,4129,4257,4389,4534,4666,4814,4910,5082,5225,5365,5504,5635,6058,6498,6647,6752,6886,7007,7157,7276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,6063", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,6138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "6282", "endColumns": "100", "endOffsets": "6378"}}]}]}