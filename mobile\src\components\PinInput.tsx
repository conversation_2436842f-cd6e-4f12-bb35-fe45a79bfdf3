import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Vibration,
  Alert
} from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY } from '../constants/theme';

interface PinInputProps {
  title: string;
  subtitle?: string;
  onPinComplete: (pin: string) => void;
  onCancel?: () => void;
  maxLength?: number;
  isLoading?: boolean;
  error?: string;
  showCancel?: boolean;
}

const PinInput: React.FC<PinInputProps> = ({
  title,
  subtitle,
  onPinComplete,
  onCancel,
  maxLength = 4,
  isLoading = false,
  error,
  showCancel = true
}) => {
  const [pin, setPin] = useState('');

  useEffect(() => {
    if (pin.length === maxLength) {
      onPinComplete(pin);
    }
  }, [pin, maxLength, onPinComplete]);

  useEffect(() => {
    if (error) {
      // Vibrate on error
      Vibration.vibrate(500);
      // Clear PIN on error
      setPin('');
    }
  }, [error]);

  const handleNumberPress = (number: string) => {
    if (pin.length < maxLength && !isLoading) {
      setPin(prev => prev + number);
    }
  };

  const handleBackspace = () => {
    if (!isLoading) {
      setPin(prev => prev.slice(0, -1));
    }
  };

  const handleClear = () => {
    if (!isLoading) {
      setPin('');
    }
  };

  const renderPinDots = () => {
    return (
      <View style={styles.pinDotsContainer}>
        {Array.from({ length: maxLength }, (_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              index < pin.length && styles.pinDotFilled,
              error && styles.pinDotError
            ]}
          />
        ))}
      </View>
    );
  };

  const renderKeypad = () => {
    const numbers = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['', '0', 'backspace']
    ];

    return (
      <View style={styles.keypadContainer}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.keypadRow}>
            {row.map((item, itemIndex) => {
              if (item === '') {
                return <View key={itemIndex} style={styles.keypadButton} />;
              }
              
              if (item === 'backspace') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[styles.keypadButton, styles.keypadButtonSpecial]}
                    onPress={handleBackspace}
                    disabled={isLoading}
                  >
                    <Text style={styles.keypadButtonSpecialText}>⌫</Text>
                  </TouchableOpacity>
                );
              }

              return (
                <TouchableOpacity
                  key={itemIndex}
                  style={[
                    styles.keypadButton,
                    isLoading && styles.keypadButtonDisabled
                  ]}
                  onPress={() => handleNumberPress(item)}
                  disabled={isLoading}
                >
                  <Text style={[
                    styles.keypadButtonText,
                    isLoading && styles.keypadButtonTextDisabled
                  ]}>
                    {item}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      {renderPinDots()}

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {isLoading && (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Verifying...</Text>
        </View>
      )}

      {renderKeypad()}

      <View style={styles.actionButtons}>
        {pin.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClear}
            disabled={isLoading}
          >
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        )}

        {showCancel && onCancel && (
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={onCancel}
            disabled={isLoading}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.xl,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
  },
  pinDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.xl,
    paddingHorizontal: SPACING.lg,
  },
  pinDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    backgroundColor: COLORS.SURFACE,
    marginHorizontal: SPACING.sm,
  },
  pinDotFilled: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  pinDotError: {
    borderColor: COLORS.ERROR,
    backgroundColor: COLORS.ERROR,
  },
  errorContainer: {
    alignItems: 'center',
    marginBottom: SPACING.md,
    minHeight: 24,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 14,
    textAlign: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    marginBottom: SPACING.md,
    minHeight: 24,
  },
  loadingText: {
    color: COLORS.PRIMARY,
    fontSize: 14,
    textAlign: 'center',
  },
  keypadContainer: {
    flex: 1,
    justifyContent: 'center',
    maxHeight: 400,
  },
  keypadRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginBottom: SPACING.md,
  },
  keypadButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.SURFACE,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  keypadButtonDisabled: {
    backgroundColor: COLORS.DISABLED,
    elevation: 0,
  },
  keypadButtonText: {
    fontSize: 24,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  keypadButtonTextDisabled: {
    color: COLORS.TEXT_DISABLED,
  },
  keypadButtonSpecial: {
    backgroundColor: COLORS.SURFACE_VARIANT,
  },
  keypadButtonSpecialText: {
    fontSize: 20,
    color: COLORS.TEXT_SECONDARY,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.lg,
    minHeight: 60,
  },
  clearButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
  },
  clearButtonText: {
    color: COLORS.PRIMARY,
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
  },
  cancelButtonText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PinInput;
