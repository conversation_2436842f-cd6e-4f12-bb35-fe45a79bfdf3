#!/usr/bin/env node

/**
 * Biometric Integration Verification Script
 * This script verifies that all biometric authentication files are properly integrated
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Biometric Integration...\n');

// Files that should exist
const requiredFiles = [
  'src/types/biometric.types.ts',
  'src/utils/biometric-constants.ts',
  'src/services/SecurityService.ts',
  'src/services/ApiService.ts',
  'src/services/BiometricAuthService.ts',
  'src/components/BiometricErrorHandler.tsx',
  'src/screens/SettingsScreen.tsx',
  'src/screens/LoginScreen.tsx',
  'src/App.tsx'
];

// Dependencies that should be installed
const requiredDependencies = [
  '@sbaiahmed1/react-native-biometrics',
  'react-native-keychain'
];

let allGood = true;

// Check files
console.log('📁 Checking required files:');
requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    allGood = false;
  }
});

console.log('\n📦 Checking dependencies:');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  requiredDependencies.forEach(dep => {
    if (allDeps[dep]) {
      console.log(`  ✅ ${dep} (${allDeps[dep]})`);
    } else {
      console.log(`  ❌ ${dep} - NOT INSTALLED`);
      allGood = false;
    }
  });
} catch (error) {
  console.log('  ❌ Could not read package.json');
  allGood = false;
}

// Check key integrations
console.log('\n🔧 Checking key integrations:');

// Check if LoginScreen imports BiometricAuthService
try {
  const loginScreen = fs.readFileSync(path.join(__dirname, 'src/screens/LoginScreen.tsx'), 'utf8');
  if (loginScreen.includes('BiometricAuthService')) {
    console.log('  ✅ LoginScreen imports BiometricAuthService');
  } else {
    console.log('  ❌ LoginScreen missing BiometricAuthService import');
    allGood = false;
  }
  
  if (loginScreen.includes('onNavigateToSettings')) {
    console.log('  ✅ LoginScreen has settings navigation');
  } else {
    console.log('  ❌ LoginScreen missing settings navigation');
    allGood = false;
  }
} catch (error) {
  console.log('  ❌ Could not read LoginScreen.tsx');
  allGood = false;
}

// Check if App.tsx imports SettingsScreen
try {
  const appFile = fs.readFileSync(path.join(__dirname, 'src/App.tsx'), 'utf8');
  if (appFile.includes('SettingsScreen')) {
    console.log('  ✅ App.tsx imports SettingsScreen');
  } else {
    console.log('  ❌ App.tsx missing SettingsScreen import');
    allGood = false;
  }
  
  if (appFile.includes('biometricAuthService')) {
    console.log('  ✅ App.tsx uses BiometricAuthService');
  } else {
    console.log('  ❌ App.tsx missing BiometricAuthService usage');
    allGood = false;
  }
} catch (error) {
  console.log('  ❌ Could not read App.tsx');
  allGood = false;
}

// Check Android permissions
console.log('\n🤖 Checking Android permissions:');
try {
  const manifest = fs.readFileSync(path.join(__dirname, 'android/app/src/main/AndroidManifest.xml'), 'utf8');
  if (manifest.includes('USE_FINGERPRINT')) {
    console.log('  ✅ USE_FINGERPRINT permission');
  } else {
    console.log('  ❌ USE_FINGERPRINT permission missing');
    allGood = false;
  }
  
  if (manifest.includes('USE_BIOMETRIC')) {
    console.log('  ✅ USE_BIOMETRIC permission');
  } else {
    console.log('  ❌ USE_BIOMETRIC permission missing');
    allGood = false;
  }
} catch (error) {
  console.log('  ❌ Could not read AndroidManifest.xml');
  allGood = false;
}

// Final result
console.log('\n' + '='.repeat(50));
if (allGood) {
  console.log('🎉 INTEGRATION VERIFICATION PASSED!');
  console.log('✅ All biometric authentication files are properly integrated');
  console.log('✅ All required dependencies are installed');
  console.log('✅ Key integrations are in place');
  console.log('✅ Android permissions are configured');
  console.log('\n🚀 Ready to build and test the app!');
  console.log('\nNext steps:');
  console.log('1. Build the app: cd android && gradlew.bat assembleDebug');
  console.log('2. Install on device: gradlew.bat installDebug');
  console.log('3. Test biometric authentication');
} else {
  console.log('❌ INTEGRATION VERIFICATION FAILED!');
  console.log('⚠️  Some files or configurations are missing');
  console.log('📋 Please check the items marked with ❌ above');
  console.log('\n🔧 To fix issues:');
  console.log('1. Ensure all required files exist');
  console.log('2. Install missing dependencies: npm install');
  console.log('3. Check file imports and integrations');
}
console.log('='.repeat(50));
