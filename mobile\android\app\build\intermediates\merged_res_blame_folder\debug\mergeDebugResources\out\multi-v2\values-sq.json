{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,6821", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,6898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,242,337,418,489,558,640,709,776,858,942,1031,1114,1184,1270,1359,1434,1515,1596,1673,1748,1821,1908,1985,2066,2140", "endColumns": "73,112,94,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "124,237,332,413,484,553,635,704,771,853,937,1026,1109,1179,1265,1354,1429,1510,1591,1668,1743,1816,1903,1980,2061,2135,2218"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2801,3175,3288,3383,5847,5918,5987,6069,6138,6205,6287,6371,6460,6543,6735,6903,6992,7067,7148,7229,7306,7381,7454,7642,7719,7800,7874", "endColumns": "73,112,94,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "2870,3283,3378,3459,5913,5982,6064,6133,6200,6282,6366,6455,6538,6608,6816,6987,7062,7143,7224,7301,7376,7449,7536,7714,7795,7869,7952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,355,468,635,728,879,994,1115,1240,1379,1522,1656,1791,1935,2031,2200,2326,2469,2617,2738,2860,2965,3106,3194,3318,3423,3564", "endColumns": "167,131,112,166,92,150,114,120,124,138,142,133,134,143,95,168,125,142,147,120,121,104,140,87,123,104,140,103", "endOffsets": "218,350,463,630,723,874,989,1110,1235,1374,1517,1651,1786,1930,2026,2195,2321,2464,2612,2733,2855,2960,3101,3189,3313,3418,3559,3663"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2875,3043,3464,3577,3744,3837,3988,4103,4224,4349,4488,4631,4765,4900,5044,5140,5309,5435,5578,5726,6613,7957,8062,8203,8291,8415,8520,8661", "endColumns": "167,131,112,166,92,150,114,120,124,138,142,133,134,143,95,168,125,142,147,120,121,104,140,87,123,104,140,103", "endOffsets": "3038,3170,3572,3739,3832,3983,4098,4219,4344,4483,4626,4760,4895,5039,5135,5304,5430,5573,5721,5842,6730,8057,8198,8286,8410,8515,8656,8760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7541", "endColumns": "100", "endOffsets": "7637"}}]}]}