{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,239,328,410,482,550,633,702,772,851,930,1015,1101,1175,1257,1341,1417,1502,1586,1666,1745,1820,1905,1981,2061,2132", "endColumns": "70,112,88,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "121,234,323,405,477,545,628,697,767,846,925,1010,1096,1170,1252,1336,1412,1497,1581,1661,1740,1815,1900,1976,2056,2127,2206"}, "to": {"startLines": "29,30,31,32,45,46,47,48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2775,2846,2959,3048,4621,4693,4761,4844,4913,4983,5062,5141,5226,5312,5386,5549,5633,5709,5794,5878,5958,6037,6112,6298,6374,6454,6525", "endColumns": "70,112,88,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "2841,2954,3043,3125,4688,4756,4839,4908,4978,5057,5136,5221,5307,5381,5463,5628,5704,5789,5873,5953,6032,6107,6192,6369,6449,6520,6599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,5468", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,5544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a26e82d508f491fb2ba1b9b8b2ee8b33\\transformed\\biometric-1.1.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,378,515,648,797,918,1048,1148,1291,1429", "endColumns": "108,92,120,136,132,148,120,129,99,142,137,116", "endOffsets": "159,252,373,510,643,792,913,1043,1143,1286,1424,1541"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3130,3239,3332,3453,3590,3723,3872,3993,4123,4223,4366,4504", "endColumns": "108,92,120,136,132,148,120,129,99,142,137,116", "endOffsets": "3234,3327,3448,3585,3718,3867,3988,4118,4218,4361,4499,4616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "6197", "endColumns": "100", "endOffsets": "6293"}}]}]}