{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,6728", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,6806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7446", "endColumns": "100", "endOffsets": "7542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,413,484,555,642,710,779,860,941,1028,1123,1197,1283,1367,1444,1525,1607,1685,1760,1834,1918,1989,2068,2139", "endColumns": "74,110,88,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "125,236,325,408,479,550,637,705,774,855,936,1023,1118,1192,1278,1362,1439,1520,1602,1680,1755,1829,1913,1984,2063,2134,2217"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2817,3172,3283,3372,5738,5809,5880,5967,6035,6104,6185,6266,6353,6448,6642,6811,6895,6972,7053,7135,7213,7288,7362,7547,7618,7697,7768", "endColumns": "74,110,88,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "2887,3278,3367,3450,5804,5875,5962,6030,6099,6180,6261,6348,6443,6517,6723,6890,6967,7048,7130,7208,7283,7357,7441,7613,7692,7763,7846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,205,335,441,607,697,838,941,1056,1183,1332,1468,1591,1726,1859,1957,2114,2236,2371,2504,2618,2738,2841,2981,3066,3188,3290,3429", "endColumns": "149,129,105,165,89,140,102,114,126,148,135,122,134,132,97,156,121,134,132,113,119,102,139,84,121,101,138,102", "endOffsets": "200,330,436,602,692,833,936,1051,1178,1327,1463,1586,1721,1854,1952,2109,2231,2366,2499,2613,2733,2836,2976,3061,3183,3285,3424,3527"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2892,3042,3455,3561,3727,3817,3958,4061,4176,4303,4452,4588,4711,4846,4979,5077,5234,5356,5491,5624,6522,7851,7954,8094,8179,8301,8403,8542", "endColumns": "149,129,105,165,89,140,102,114,126,148,135,122,134,132,97,156,121,134,132,113,119,102,139,84,121,101,138,102", "endOffsets": "3037,3167,3556,3722,3812,3953,4056,4171,4298,4447,4583,4706,4841,4974,5072,5229,5351,5486,5619,5733,6637,7949,8089,8174,8296,8398,8537,8640"}}]}]}