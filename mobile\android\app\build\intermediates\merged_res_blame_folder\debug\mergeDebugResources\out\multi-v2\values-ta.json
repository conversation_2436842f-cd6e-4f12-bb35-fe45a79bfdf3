{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,235,326,406,478,546,630,700,767,843,921,1003,1083,1154,1236,1318,1396,1485,1575,1656,1728,1798,1892,1967,2050,2119", "endColumns": "74,104,90,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "125,230,321,401,473,541,625,695,762,838,916,998,1078,1149,1231,1313,1391,1480,1570,1651,1723,1793,1887,1962,2045,2114,2192"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2871,3247,3352,3443,5866,5938,6006,6090,6160,6227,6303,6381,6463,6543,6743,6907,6989,7067,7156,7246,7327,7399,7469,7664,7739,7822,7891", "endColumns": "74,104,90,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "2941,3347,3438,3518,5933,6001,6085,6155,6222,6298,6376,6458,6538,6609,6820,6984,7062,7151,7241,7322,7394,7464,7558,7734,7817,7886,7964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,6825", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,6902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7563", "endColumns": "100", "endOffsets": "7659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,356,476,656,748,903,1027,1134,1248,1365,1496,1623,1746,1885,1984,2150,2280,2421,2572,2699,2828,2934,3071,3166,3292,3395,3532", "endColumns": "165,134,119,179,91,154,123,106,113,116,130,126,122,138,98,165,129,140,150,126,128,105,136,94,125,102,136,107", "endOffsets": "216,351,471,651,743,898,1022,1129,1243,1360,1491,1618,1741,1880,1979,2145,2275,2416,2567,2694,2823,2929,3066,3161,3287,3390,3527,3635"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2946,3112,3523,3643,3823,3915,4070,4194,4301,4415,4532,4663,4790,4913,5052,5151,5317,5447,5588,5739,6614,7969,8075,8212,8307,8433,8536,8673", "endColumns": "165,134,119,179,91,154,123,106,113,116,130,126,122,138,98,165,129,140,150,126,128,105,136,94,125,102,136,107", "endOffsets": "3107,3242,3638,3818,3910,4065,4189,4296,4410,4527,4658,4785,4908,5047,5146,5312,5442,5583,5734,5861,6738,8070,8207,8302,8428,8531,8668,8776"}}]}]}