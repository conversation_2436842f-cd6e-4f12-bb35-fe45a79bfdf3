{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "35,36,37,39,40,41,42,43,44,45,46,47,48,51,52,53,54,56,57,58,59,60,61,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,200,201,203,204,205,206,207,208,209,229,230,231,232,233,234,235,236,282,283,284,285,289,295,296,301,320,328,329,330,331,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,416,430,431,432,433,434,447,455,456,460,464,468,473,479,486,490,494,499,503,507,511,515,519,523,529,533,539,543,549,553,558,562,565,569,575,579,585,589,595,598,602,606,610,614,618,619,620,621,624,627,630,633,637,638,639,640,641,644,646,648,650,655,656,660,666,670,671,673,685,686,690,696,700,701,702,706,733,737,738,742,770,942,968,1139,1165,1196,1204,1210,1226,1248,1253,1258,1268,1277,1286,1290,1297,1316,1323,1324,1333,1336,1339,1343,1347,1351,1354,1355,1360,1365,1375,1380,1387,1393,1394,1397,1401,1406,1408,1410,1413,1416,1418,1422,1425,1432,1435,1438,1442,1444,1448,1450,1452,1454,1458,1466,1474,1486,1492,1501,1504,1515,1518,1519,1524,1525,1545,1614,1684,1685,1695,1704,1705,1707,1711,1714,1717,1720,1723,1726,1729,1732,1736,1739,1742,1745,1749,1752,1756,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1788,1790,1791,1792,1793,1794,1795,1796,1797,1799,1800,1802,1803,1805,1807,1808,1810,1811,1812,1813,1814,1815,1817,1818,1819,1820,1821,1838,1840,1842,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1859,1860,1861,1862,1863,1864,1865,1867,1871,1933,1934,1935,1936,1937,1938,1942,1943,1944,1945,1947,1949,1951,1953,1955,1956,1957,1958,1960,1962,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1978,1979,1980,1981,1983,1985,1986,1988,1989,1991,1993,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2008,2009,2010,2011,2013,2014,2015,2016,2017,2019,2021,2023,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2089,2164,2167,2170,2173,2187,2193,2235,2238,2267,2294,2303,2367,2744,2754,2792,2910,3164,3188,3194,3200,3221,3345,3365,3371,3375,3381,3499,3535,3601,3621,3676,3688,3714", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1260,1315,1360,1469,1510,1565,1627,1691,1761,1822,1897,1973,2050,2288,2373,2455,2531,2663,2740,2818,2924,3030,3109,3189,3246,3430,3504,3579,3644,3710,3770,3831,3903,3976,4043,4111,4170,4229,4288,4347,4406,4460,4514,4567,4621,4675,4729,5004,5078,5157,5230,5304,5375,5447,5519,5592,5649,5707,5780,5854,5928,6003,6075,6148,6218,6289,6349,6410,6479,6548,6618,6692,6768,6832,6909,6985,7062,7127,7196,7273,7348,7417,7485,7562,7628,7689,7786,7851,7920,8019,8090,8149,8207,8264,8323,8387,8458,8530,8602,8674,8746,8813,8881,8949,9008,9071,9135,9225,9316,9376,9442,9509,9575,9645,9709,9762,9829,9890,9957,10070,10128,10191,10256,10321,10396,10469,10541,10585,10632,10678,10727,10788,10849,10910,10972,11036,11100,11164,11229,11292,11352,11413,11479,11538,11598,11660,11731,11791,12415,12501,12641,12731,12818,12906,12988,13071,13161,14442,14494,14552,14597,14663,14727,14784,14841,17532,17589,17637,17686,17920,18159,18206,18439,19429,19834,19898,19960,20020,20280,20354,20424,20502,20556,20626,20711,20759,20805,20866,20929,20995,21059,21130,21193,21258,21322,21383,21444,21496,21569,21643,21712,21787,21861,21935,22076,28317,30194,30272,30362,30450,30546,31315,31897,31986,32233,32514,32766,33051,33444,33921,34143,34365,34641,34868,35098,35328,35558,35788,36015,36434,36660,37085,37315,37743,37962,38245,38453,38584,38811,39237,39462,39889,40110,40535,40655,40931,41232,41556,41847,42161,42298,42429,42534,42776,42943,43147,43355,43626,43738,43850,43955,44072,44286,44432,44572,44658,45006,45094,45340,45758,46007,46089,46187,46844,46944,47196,47620,47875,47969,48058,48295,50319,50561,50663,50916,53072,63753,65269,75964,77492,79249,79875,80295,81556,82821,83077,83313,83860,84354,84959,85157,85737,87105,87480,87598,88136,88293,88489,88762,89018,89188,89329,89393,89758,90125,90801,91065,91403,91756,91850,92036,92342,92604,92729,92856,93095,93306,93425,93618,93795,94250,94431,94553,94812,94925,95112,95214,95321,95450,95725,96233,96729,97606,97900,98470,98619,99351,99523,99607,99943,100035,101174,106405,111776,111838,112416,113000,113091,113204,113433,113593,113745,113916,114082,114251,114418,114581,114824,114994,115167,115338,115612,115811,116016,116736,116820,116916,117012,117110,117210,117312,117414,117516,117618,117720,117820,117916,118028,118157,118280,118411,118542,118640,118754,118848,118988,119122,119218,119330,119430,119546,119642,119754,119854,119994,120130,120294,120424,120582,120732,120873,121017,121152,121264,121414,121542,121670,121806,121938,122068,122198,122310,123590,123736,123880,124044,124110,124200,124276,124380,124470,124572,124680,124788,124888,124968,125060,125158,125268,125320,125398,125504,125596,125700,125810,125932,126095,129533,129613,129713,129803,129913,130003,130244,130338,130444,130536,130636,130748,130862,130978,131094,131188,131302,131414,131516,131636,131758,131840,131944,132064,132190,132288,132382,132470,132582,132698,132820,132932,133107,133223,133309,133401,133513,133637,133704,133830,133898,134026,134170,134298,134367,134462,134577,134690,134789,134898,135009,135120,135221,135326,135426,135556,135647,135770,135864,135976,136062,136166,136262,136350,136468,136572,136676,136802,136890,136998,137098,137188,137298,137382,137484,137568,137622,137686,137792,137878,137988,138072,140803,143419,143537,143652,143732,144093,144326,145730,145808,147152,148513,148901,151744,162383,162721,164392,170476,178479,179230,179492,179692,180071,184349,184955,185184,185335,185550,188078,189099,192125,192869,195000,195340,196651", "endLines": "35,36,37,39,40,41,42,43,44,45,46,47,48,51,52,53,54,56,57,58,59,60,61,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,200,201,203,204,205,206,207,208,209,229,230,231,232,233,234,235,236,282,283,284,285,289,295,296,301,320,328,329,330,331,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,416,430,431,432,433,434,454,455,459,463,467,472,478,485,489,493,498,502,506,510,514,518,522,528,532,538,542,548,552,557,561,564,568,574,578,584,588,594,597,601,605,609,613,617,618,619,620,623,626,629,632,636,637,638,639,640,643,645,647,649,654,655,659,665,669,670,672,684,685,689,695,699,700,701,705,732,736,737,741,769,941,967,1138,1164,1195,1203,1209,1225,1247,1252,1257,1267,1276,1285,1289,1296,1315,1322,1323,1332,1335,1338,1342,1346,1350,1353,1354,1359,1364,1374,1379,1386,1392,1393,1396,1400,1405,1407,1409,1412,1415,1417,1421,1424,1431,1434,1437,1441,1443,1447,1449,1451,1453,1457,1465,1473,1485,1491,1500,1503,1514,1517,1518,1523,1524,1529,1613,1683,1684,1694,1703,1704,1706,1710,1713,1716,1719,1722,1725,1728,1731,1735,1738,1741,1744,1748,1751,1755,1759,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1787,1789,1790,1791,1792,1793,1794,1795,1796,1798,1799,1801,1802,1804,1806,1807,1809,1810,1811,1812,1813,1814,1816,1817,1818,1819,1820,1821,1839,1841,1843,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1858,1859,1860,1861,1862,1863,1864,1866,1870,1874,1933,1934,1935,1936,1937,1941,1942,1943,1944,1946,1948,1950,1952,1954,1955,1956,1957,1959,1961,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1977,1978,1979,1980,1982,1984,1985,1987,1988,1990,1992,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2007,2008,2009,2010,2012,2013,2014,2015,2016,2018,2020,2022,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2163,2166,2169,2172,2186,2192,2202,2237,2266,2293,2302,2366,2729,2747,2781,2819,2927,3187,3193,3199,3220,3344,3364,3370,3374,3380,3415,3510,3600,3620,3675,3687,3713,3720", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1310,1355,1404,1505,1560,1622,1686,1756,1817,1892,1968,2045,2123,2368,2450,2526,2602,2735,2813,2919,3025,3104,3184,3241,3299,3499,3574,3639,3705,3765,3826,3898,3971,4038,4106,4165,4224,4283,4342,4401,4455,4509,4562,4616,4670,4724,4778,5073,5152,5225,5299,5370,5442,5514,5587,5644,5702,5775,5849,5923,5998,6070,6143,6213,6284,6344,6405,6474,6543,6613,6687,6763,6827,6904,6980,7057,7122,7191,7268,7343,7412,7480,7557,7623,7684,7781,7846,7915,8014,8085,8144,8202,8259,8318,8382,8453,8525,8597,8669,8741,8808,8876,8944,9003,9066,9130,9220,9311,9371,9437,9504,9570,9640,9704,9757,9824,9885,9952,10065,10123,10186,10251,10316,10391,10464,10536,10580,10627,10673,10722,10783,10844,10905,10967,11031,11095,11159,11224,11287,11347,11408,11474,11533,11593,11655,11726,11786,11854,12496,12583,12726,12813,12901,12983,13066,13156,13247,14489,14547,14592,14658,14722,14779,14836,14890,17584,17632,17681,17732,17949,18201,18250,18480,19456,19893,19955,20015,20072,20349,20419,20497,20551,20621,20706,20754,20800,20861,20924,20990,21054,21125,21188,21253,21317,21378,21439,21491,21564,21638,21707,21782,21856,21930,22071,22141,28365,30267,30357,30445,30541,30631,31892,31981,32228,32509,32761,33046,33439,33916,34138,34360,34636,34863,35093,35323,35553,35783,36010,36429,36655,37080,37310,37738,37957,38240,38448,38579,38806,39232,39457,39884,40105,40530,40650,40926,41227,41551,41842,42156,42293,42424,42529,42771,42938,43142,43350,43621,43733,43845,43950,44067,44281,44427,44567,44653,45001,45089,45335,45753,46002,46084,46182,46839,46939,47191,47615,47870,47964,48053,48290,50314,50556,50658,50911,53067,63748,65264,75959,77487,79244,79870,80290,81551,82816,83072,83308,83855,84349,84954,85152,85732,87100,87475,87593,88131,88288,88484,88757,89013,89183,89324,89388,89753,90120,90796,91060,91398,91751,91845,92031,92337,92599,92724,92851,93090,93301,93420,93613,93790,94245,94426,94548,94807,94920,95107,95209,95316,95445,95720,96228,96724,97601,97895,98465,98614,99346,99518,99602,99938,100030,100308,106400,111771,111833,112411,112995,113086,113199,113428,113588,113740,113911,114077,114246,114413,114576,114819,114989,115162,115333,115607,115806,116011,116341,116815,116911,117007,117105,117205,117307,117409,117511,117613,117715,117815,117911,118023,118152,118275,118406,118537,118635,118749,118843,118983,119117,119213,119325,119425,119541,119637,119749,119849,119989,120125,120289,120419,120577,120727,120868,121012,121147,121259,121409,121537,121665,121801,121933,122063,122193,122305,122445,123731,123875,124013,124105,124195,124271,124375,124465,124567,124675,124783,124883,124963,125055,125153,125263,125315,125393,125499,125591,125695,125805,125927,126090,126247,129608,129708,129798,129908,129998,130239,130333,130439,130531,130631,130743,130857,130973,131089,131183,131297,131409,131511,131631,131753,131835,131939,132059,132185,132283,132377,132465,132577,132693,132815,132927,133102,133218,133304,133396,133508,133632,133699,133825,133893,134021,134165,134293,134362,134457,134572,134685,134784,134893,135004,135115,135216,135321,135421,135551,135642,135765,135859,135971,136057,136161,136257,136345,136463,136567,136671,136797,136885,136993,137093,137183,137293,137377,137479,137563,137617,137681,137787,137873,137983,138067,138187,143414,143532,143647,143727,144088,144321,144838,145803,147147,148508,148896,151739,161792,162513,164086,165744,171043,179225,179487,179687,180066,184344,184950,185179,185330,185545,186628,188385,192120,192864,194995,195335,196646,196849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4355c535250bc31a3bccfed833d3398f\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "34,2076,2820,2826", "startColumns": "4,4,4,4", "startOffsets": "1199,140217,165749,165960", "endLines": "34,2078,2825,2909", "endColumns": "60,12,24,24", "endOffsets": "1255,140357,165955,170471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0c83e5efdb8d69442996ff6b9e38b7cf\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "298,324", "startColumns": "4,4", "startOffsets": "18298,19606", "endColumns": "41,59", "endOffsets": "18335,19661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ac726015019e861dce070d698b39c109\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2203,2219,2225,3511,3527", "startColumns": "4,4,4,4,4", "startOffsets": "144843,145268,145446,188390,188801", "endLines": "2218,2224,2234,3526,3530", "endColumns": "24,24,24,24,24", "endOffsets": "145263,145441,145725,188796,188923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4671ada73b10174dff6ecba084f7c23f\\transformed\\jetified-drawee-3.1.3\\res\\values\\values.xml", "from": {"startLines": "2,136", "startColumns": "4,4", "startOffsets": "55,3906", "endLines": "135,218", "endColumns": "22,22", "endOffsets": "3901,5346"}, "to": {"startLines": "2991,3416", "startColumns": "4,4", "startOffsets": "173779,186633", "endLines": "3124,3498", "endColumns": "22,22", "endOffsets": "177625,188073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4c74eea83f6f83beab22cbcba5b3f2af\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "326", "startColumns": "4", "startOffsets": "19720", "endColumns": "49", "endOffsets": "19765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a26e82d508f491fb2ba1b9b8b2ee8b33\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,55,202,393,394,395,396,397,398,399,400,401,402,403,404", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,2607,12588,25796,25872,25932,26021,26120,26228,26325,26413,26513,26583,26680,26790", "endLines": "5,7,10,14,33,55,202,393,394,395,396,397,398,399,400,401,402,403,404", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "284,367,478,613,1194,2658,12636,25867,25927,26016,26115,26223,26320,26408,26508,26578,26675,26785,26874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\053935b21644a540e4de15f1d031a3a1\\transformed\\fragment-1.8.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "288,300,327,2982,2987", "startColumns": "4,4,4,4,4", "startOffsets": "17863,18374,19770,173460,173630", "endLines": "288,300,327,2986,2990", "endColumns": "56,64,63,24,24", "endOffsets": "17915,18434,19829,173625,173774"}}, {"source": "C:\\Users\\<USER>\\Node\\autoflow\\mobile\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "45", "endOffsets": "57"}, "to": {"startLines": "364", "startColumns": "4", "startOffsets": "22346", "endColumns": "45", "endOffsets": "22387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\98922406aa30e38ed245b1e87bdedd94\\transformed\\jetified-flipper-0.201.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,124", "endColumns": "68,56", "endOffsets": "119,176"}, "to": {"startLines": "286,287", "startColumns": "4,4", "startOffsets": "17737,17806", "endColumns": "68,56", "endOffsets": "17801,17858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\da3e3798b330ef3e05862728f0b02188\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "3531", "startColumns": "4", "startOffsets": "188928", "endLines": "3534", "endColumns": "24", "endOffsets": "189094"}}, {"source": "C:\\Users\\<USER>\\Node\\autoflow\\mobile\\android\\app\\build\\generated\\res\\resValues\\debug\\values\\gradleResValues.xml", "from": {"startLines": "6,8", "startColumns": "4,4", "startOffsets": "159,265", "endColumns": "63,68", "endOffsets": "218,329"}, "to": {"startLines": "332,333", "startColumns": "4,4", "startOffsets": "20077,20141", "endColumns": "63,68", "endOffsets": "20136,20205"}}, {"source": "C:\\Users\\<USER>\\Node\\autoflow\\mobile\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "54", "endLines": "6", "endColumns": "12", "endOffsets": "267"}, "to": {"startLines": "443", "startColumns": "4", "startOffsets": "31132", "endLines": "446", "endColumns": "12", "endOffsets": "31310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cd542cf04f0173883ff2e0ddfee08a56\\transformed\\jetified-autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "192,1875,2040,2041,2048,2053,2058,2065,2730", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11859,126252,138192,138252,138634,138914,139196,139580,161797", "endLines": "192,1890,2040,2047,2052,2057,2064,2073,2743", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "11922,127058,138247,138629,138909,139191,139575,140073,162378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,94,95,96,97,103,113,146,167,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,344,407,477,545,617,687,748,822,895,956,1017,1079,1143,1205,1266,1334,1434,1494,1560,1633,1702,1759,1811,1873,1945,2021,2086,2145,2204,2264,2324,2384,2444,2504,2564,2624,2684,2744,2804,2863,2923,2983,3043,3103,3163,3223,3283,3343,3403,3463,3522,3582,3642,3701,3760,3819,3878,3937,3996,4031,4066,4121,4184,4239,4297,4355,4416,4479,4536,4587,4637,4698,4755,4821,4855,4890,4925,4995,5066,5183,5384,5494,5695,5824,5896,5963,6166,6467,8198,8879,9561", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,88,89,93,94,95,96,102,112,145,166,199,205", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,339,402,472,540,612,682,743,817,890,951,1012,1074,1138,1200,1261,1329,1429,1489,1555,1628,1697,1754,1806,1868,1940,2016,2081,2140,2199,2259,2319,2379,2439,2499,2559,2619,2679,2739,2799,2858,2918,2978,3038,3098,3158,3218,3278,3338,3398,3458,3517,3577,3637,3696,3755,3814,3873,3932,3991,4026,4061,4116,4179,4234,4292,4350,4411,4474,4531,4582,4632,4693,4750,4816,4850,4885,4920,4990,5061,5178,5379,5489,5690,5819,5891,5958,6161,6462,8193,8874,9556,9723"}, "to": {"startLines": "38,49,50,88,89,193,194,195,196,197,198,199,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,237,238,239,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,292,293,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,334,425,1822,1823,1828,1831,1836,2074,2075,2748,2782,2928,2961,3125,3158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,2128,2200,4783,4852,11927,11997,12065,12137,12207,12268,12342,13252,13313,13374,13436,13500,13562,13623,13691,13791,13851,13917,13990,14059,14116,14168,14895,14967,15043,15272,15331,15390,15450,15510,15570,15630,15690,15750,15810,15870,15930,15990,16049,16109,16169,16229,16289,16349,16409,16469,16529,16589,16649,16708,16768,16828,16887,16946,17005,17064,17123,18045,18080,18485,18540,18603,18658,18716,18774,18835,18898,18955,19006,19056,19117,19174,19240,19274,19309,20210,29490,122450,122567,122834,123127,123394,140078,140150,162518,164091,171048,172779,177630,178312", "endLines": "38,49,50,88,89,193,194,195,196,197,198,199,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,237,238,239,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,292,293,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,334,425,1822,1826,1828,1834,1836,2074,2075,2753,2791,2960,2981,3157,3163", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1464,2195,2283,4847,4910,11992,12060,12132,12202,12263,12337,12410,13308,13369,13431,13495,13557,13618,13686,13786,13846,13912,13985,14054,14111,14163,14225,14962,15038,15103,15326,15385,15445,15505,15565,15625,15685,15745,15805,15865,15925,15985,16044,16104,16164,16224,16284,16344,16404,16464,16524,16584,16644,16703,16763,16823,16882,16941,17000,17059,17118,17177,18075,18110,18535,18598,18653,18711,18769,18830,18893,18950,19001,19051,19112,19169,19235,19269,19304,19339,20275,29556,122562,122763,122939,123323,123518,140145,140212,162716,164387,172774,173455,178307,178474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b1d3d828f48de9c2ad857eb4830aec4\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "90,225,226,227,228,1827,1829,1830,1835,1837", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4915,14230,14283,14336,14389,122768,122944,123066,123328,123523", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "4999,14278,14331,14384,14437,122829,123061,123122,123389,123585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f1892997b385bd5c7669531b7771676c\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "19563", "endColumns": "42", "endOffsets": "19601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4f242b40d56bcfb89f4a8d05890ae23c\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "22263", "endColumns": "82", "endOffsets": "22341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,81,85,89,92,96,100,103,106,107,108,117,124,131,134,137,140,146,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,213,276,327,381,440,488,537,586,634,683,741,790,840,881,925,968,1002,1041,1087,1141,1322,1439,1562,1658,1778,1898,2000,2119,2230,2337,2440,2551,2720,2888,3005,3115,3230,3343,3529,3637,3750,3841,3952,4121,4219,4346,4471,4566,4673,4843,4941,5124,5297,5409,5510,5669,5803,5943,6045,6150,6281,6450,6567,6715,6860,7010,7109,7205,7401,7584,7683,7867,8034,8282,8530,8798,8983,9185,9391,9592,9781,9807,9842,10380,10798,11176,11353,11532,11715,12080,12277", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,80,84,88,91,95,99,102,105,106,107,116,123,130,133,136,139,145,148,158", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,49,40,43,42,33,38,45,53,47,116,122,95,119,119,101,118,110,106,102,110,168,167,116,109,114,112,185,107,112,90,110,168,97,126,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "208,271,322,376,435,483,532,581,629,678,736,785,835,876,920,963,997,1036,1082,1136,1184,1434,1557,1653,1773,1893,1995,2114,2225,2332,2435,2546,2715,2883,3000,3110,3225,3338,3524,3632,3745,3836,3947,4116,4214,4341,4466,4561,4668,4838,4936,5119,5292,5404,5505,5664,5798,5938,6040,6145,6276,6445,6562,6710,6855,7005,7104,7200,7396,7579,7678,7862,8029,8277,8525,8793,8978,9180,9386,9587,9776,9802,9837,10375,10793,11171,11348,11527,11710,12075,12272,12713"}, "to": {"startLines": "64,65,240,241,242,275,276,277,278,279,280,281,290,291,294,297,299,318,319,321,322,362,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,405,406,407,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,426,427,428,429,435,439,1530,1534,1537,1541,1760,1763,1844,1891,1892,1901,1908,1915,1918,1921,1924,1930,2079", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3304,3367,15108,15159,15213,17182,17230,17279,17328,17376,17425,17483,17954,18004,18115,18255,18340,19344,19383,19461,19515,22146,22392,22515,22611,22731,22851,22953,23072,23183,23290,23393,23504,23673,23841,23958,24068,24183,24296,24482,24590,24703,24794,24905,25074,25172,25299,25424,25519,25626,26879,26977,27160,27333,27445,27546,27705,27839,27979,28081,28186,28370,28539,28656,28804,28949,29099,29198,29294,29561,29744,29843,30027,30636,30884,100313,100581,100766,100968,116346,116547,124018,127063,127098,127636,128054,128432,128609,128788,128971,129336,140362", "endLines": "64,65,240,241,242,275,276,277,278,279,280,281,290,291,294,297,299,318,319,321,322,362,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,405,406,407,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,426,427,428,429,438,442,1533,1536,1540,1544,1762,1765,1844,1891,1900,1907,1914,1917,1920,1923,1929,1932,2088", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,49,40,43,42,33,38,45,53,47,116,122,95,119,119,101,118,110,106,102,110,168,167,116,109,114,112,185,107,112,90,110,168,97,126,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "3362,3425,15154,15208,15267,17225,17274,17323,17371,17420,17478,17527,17999,18040,18154,18293,18369,19378,19424,19510,19558,22258,22510,22606,22726,22846,22948,23067,23178,23285,23388,23499,23668,23836,23953,24063,24178,24291,24477,24585,24698,24789,24900,25069,25167,25294,25419,25514,25621,25791,26972,27155,27328,27440,27541,27700,27834,27974,28076,28181,28312,28534,28651,28799,28944,29094,29193,29289,29485,29739,29838,30022,30189,30879,31127,100576,100761,100963,101169,116542,116731,124039,127093,127631,128049,128427,128604,128783,128966,129331,129528,140798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\df458c019b9b63d0ceef53ab6be68984\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "325", "startColumns": "4", "startOffsets": "19666", "endColumns": "53", "endOffsets": "19715"}}]}]}