import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { BiometricService } from '../services/biometric';

const BiometricTest: React.FC = () => {
  const [biometricService] = useState(new BiometricService());
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testBiometricAvailability = async () => {
    setIsLoading(true);
    addResult('Testing biometric availability...');
    
    try {
      const isAvailable = await biometricService.isBiometricAvailable();
      addResult(`Biometric available: ${isAvailable}`);
      
      if (isAvailable) {
        const biometricType = await biometricService.getBiometricType();
        addResult(`Biometric type: ${biometricType || 'Unknown'}`);
      }
    } catch (error: any) {
      addResult(`Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testBiometricPrompt = async () => {
    setIsLoading(true);
    addResult('Testing biometric prompt...');
    
    try {
      const isAvailable = await biometricService.isBiometricAvailable();
      if (!isAvailable) {
        addResult('Biometric not available - skipping prompt test');
        return;
      }

      // Test the raw react-native-biometrics prompt
      const ReactNativeBiometrics = require('react-native-biometrics').default;
      const rnBiometrics = new ReactNativeBiometrics();
      
      addResult('Showing biometric prompt...');
      const result = await rnBiometrics.simplePrompt({
        promptMessage: 'Test biometric authentication',
        cancelButtonText: 'Cancel',
      });
      
      addResult(`Prompt result: ${JSON.stringify(result)}`);
      
      if (result.success) {
        addResult('✅ Biometric authentication successful!');
      } else {
        addResult(`❌ Biometric authentication failed: ${result.error || 'Unknown error'}`);
      }
    } catch (error: any) {
      addResult(`❌ Error during prompt: ${error.message}`);
      console.error('Biometric test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testEnableBiometric = async () => {
    setIsLoading(true);
    addResult('Testing enable biometric...');
    
    try {
      await biometricService.enableBiometric('<EMAIL>', 'test-user-id');
      addResult('✅ Biometric enabled successfully!');
    } catch (error: any) {
      addResult(`❌ Error enabling biometric: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testGetCredentials = async () => {
    setIsLoading(true);
    addResult('Testing get biometric credentials...');
    
    try {
      const credentials = await biometricService.getBiometricCredentials();
      if (credentials) {
        addResult(`✅ Credentials retrieved: ${JSON.stringify(credentials)}`);
      } else {
        addResult('❌ No credentials found');
      }
    } catch (error: any) {
      addResult(`❌ Error getting credentials: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDisableBiometric = async () => {
    setIsLoading(true);
    addResult('Testing disable biometric...');
    
    try {
      await biometricService.disableBiometric();
      addResult('✅ Biometric disabled successfully!');
    } catch (error: any) {
      addResult(`❌ Error disabling biometric: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Biometric Authentication Test</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testBiometricAvailability}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Availability</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testBiometricPrompt}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Prompt</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testEnableBiometric}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Enable Biometric</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testGetCredentials}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Get Credentials</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testDisableBiometric}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Disable Biometric</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    maxHeight: 300,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  resultText: {
    fontSize: 12,
    marginBottom: 5,
    color: '#666',
    fontFamily: 'monospace',
  },
});

export default BiometricTest;
