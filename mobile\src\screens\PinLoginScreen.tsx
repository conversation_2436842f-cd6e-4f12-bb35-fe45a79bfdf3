import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  Alert
} from 'react-native';
import { COLORS, SPACING } from '../constants/theme';
import PinInput from '../components/PinInput';
import { pinAuthService } from '../services/PinAuthService';

interface PinLoginScreenProps {
  userId: string;
  userEmail: string;
  onLoginSuccess: () => void;
  onUsePassword: () => void;
  onForgotPin?: () => void;
}

const PinLoginScreen: React.FC<PinLoginScreenProps> = ({
  userId,
  userEmail,
  onLoginSuccess,
  onUsePassword,
  onForgotPin
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [lockoutStatus, setLockoutStatus] = useState<{
    isLockedOut: boolean;
    remainingTime?: number;
  }>({ isLockedOut: false });

  useEffect(() => {
    checkLockoutStatus();
    
    // Update lockout status every second if locked out
    const interval = setInterval(() => {
      if (lockoutStatus.isLockedOut) {
        checkLockoutStatus();
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [lockoutStatus.isLockedOut]);

  const checkLockoutStatus = async () => {
    try {
      const status = await pinAuthService.getLockoutStatus();
      setLockoutStatus(status);
      
      if (status.isLockedOut) {
        const minutes = Math.ceil((status.remainingTime || 0) / 60);
        setError(`Too many failed attempts. Try again in ${minutes} minute${minutes !== 1 ? 's' : ''}.`);
      } else {
        setError('');
      }
    } catch (error) {
      console.error('Error checking lockout status:', error);
    }
  };

  const handlePinComplete = async (pin: string) => {
    if (lockoutStatus.isLockedOut) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await pinAuthService.authenticateWithPin(userId, pin);
      
      if (result.success) {
        onLoginSuccess();
      } else {
        setError(result.error || 'Authentication failed');
        await checkLockoutStatus(); // Update lockout status after failed attempt
      }
    } catch (error) {
      console.error('PIN authentication error:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPin = () => {
    Alert.alert(
      'Forgot PIN?',
      'You can use your password to login or reset your PIN in settings after logging in.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Use Password', onPress: onUsePassword },
        ...(onForgotPin ? [{ text: 'Reset PIN', onPress: onForgotPin }] : [])
      ]
    );
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.BACKGROUND} />
      
      <View style={styles.header}>
        <Text style={styles.welcomeText}>Welcome back!</Text>
        <Text style={styles.emailText}>{userEmail}</Text>
        <Text style={styles.instructionText}>Enter your PIN to continue</Text>
      </View>

      <View style={styles.content}>
        {lockoutStatus.isLockedOut ? (
          <View style={styles.lockoutContainer}>
            <Text style={styles.lockoutIcon}>🔒</Text>
            <Text style={styles.lockoutTitle}>Account Temporarily Locked</Text>
            <Text style={styles.lockoutMessage}>
              Too many failed PIN attempts.{'\n'}
              Please try again in {formatTime(lockoutStatus.remainingTime || 0)}
            </Text>
            <TouchableOpacity
              style={styles.usePasswordButton}
              onPress={onUsePassword}
            >
              <Text style={styles.usePasswordButtonText}>Use Password Instead</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <PinInput
            title="Enter your PIN"
            subtitle="Use your 4-digit PIN to access your account"
            onPinComplete={handlePinComplete}
            onCancel={onUsePassword}
            maxLength={4}
            isLoading={isLoading}
            error={error}
            showCancel={false}
          />
        )}
      </View>

      {!lockoutStatus.isLockedOut && (
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.alternativeButton}
            onPress={onUsePassword}
            disabled={isLoading}
          >
            <Text style={styles.alternativeButtonText}>Use Password Instead</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.forgotButton}
            onPress={handleForgotPin}
            disabled={isLoading}
          >
            <Text style={styles.forgotButtonText}>Forgot PIN?</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.xl * 2,
    paddingBottom: SPACING.lg,
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.xs,
  },
  emailText: {
    fontSize: 16,
    color: COLORS.PRIMARY,
    marginBottom: SPACING.sm,
  },
  instructionText: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  lockoutContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  lockoutIcon: {
    fontSize: 64,
    marginBottom: SPACING.lg,
  },
  lockoutTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.ERROR,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  lockoutMessage: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.xl,
  },
  usePasswordButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: 8,
  },
  usePasswordButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
    gap: SPACING.md,
  },
  alternativeButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    backgroundColor: COLORS.SURFACE,
  },
  alternativeButtonText: {
    color: COLORS.PRIMARY,
    fontSize: 16,
    fontWeight: '600',
  },
  forgotButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
  },
  forgotButtonText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: 14,
    textDecorationLine: 'underline',
  },
});

export default PinLoginScreen;
