{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,289,357,439,506,582,658,741,828,909,992,1072,1158,1243,1321,1392,1462,1553,1628,1703", "endColumns": "74,82,75,67,81,66,75,75,82,86,80,82,79,85,84,77,70,69,90,74,74,77", "endOffsets": "125,208,284,352,434,501,577,653,736,823,904,987,1067,1153,1238,1316,1387,1457,1548,1623,1698,1776"}, "to": {"startLines": "29,32,51,52,53,54,55,56,57,58,60,62,63,64,65,66,67,68,69,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2832,3217,5749,5825,5893,5975,6042,6118,6194,6277,6492,6655,6738,6818,6904,6989,7067,7138,7208,7400,7475,7550", "endColumns": "74,82,75,67,81,66,75,75,82,86,80,82,79,85,84,77,70,69,90,74,74,77", "endOffsets": "2902,3295,5820,5888,5970,6037,6113,6189,6272,6359,6568,6733,6813,6899,6984,7062,7133,7203,7294,7470,7545,7623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,6573", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,6650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "70", "startColumns": "4", "startOffsets": "7299", "endColumns": "100", "endOffsets": "7395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,365,474,651,746,910,1036,1151,1270,1402,1537,1678,1800,1957,2056,2223,2352,2498,2686,2814,2942,3054,3200,3301,3436,3540,3678", "endColumns": "173,135,108,176,94,163,125,114,118,131,134,140,121,156,98,166,128,145,187,127,127,111,145,100,134,103,137,103", "endOffsets": "224,360,469,646,741,905,1031,1146,1265,1397,1532,1673,1795,1952,2051,2218,2347,2493,2681,2809,2937,3049,3195,3296,3431,3535,3673,3777"}, "to": {"startLines": "30,31,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,59,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2907,3081,3300,3409,3586,3681,3845,3971,4086,4205,4337,4472,4613,4735,4892,4991,5158,5287,5433,5621,6364,7628,7740,7886,7987,8122,8226,8364", "endColumns": "173,135,108,176,94,163,125,114,118,131,134,140,121,156,98,166,128,145,187,127,127,111,145,100,134,103,137,103", "endOffsets": "3076,3212,3404,3581,3676,3840,3966,4081,4200,4332,4467,4608,4730,4887,4986,5153,5282,5428,5616,5744,6487,7735,7881,7982,8117,8221,8359,8463"}}]}]}