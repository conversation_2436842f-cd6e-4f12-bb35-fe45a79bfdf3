{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,5569", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,5650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,243,336,422,493,563,646,713,780,859,938,1026,1119,1187,1273,1358,1434,1517,1599,1674,1752,1826,1912,1984,2063,2139", "endColumns": "69,117,92,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "120,238,331,417,488,558,641,708,775,854,933,1021,1114,1182,1268,1353,1429,1512,1594,1669,1747,1821,1907,1979,2058,2134,2220"}, "to": {"startLines": "29,30,31,32,45,46,47,48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2843,2913,3031,3124,4718,4789,4859,4942,5009,5076,5155,5234,5322,5415,5483,5655,5740,5816,5899,5981,6056,6134,6208,6395,6467,6546,6622", "endColumns": "69,117,92,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "2908,3026,3119,3205,4784,4854,4937,5004,5071,5150,5229,5317,5410,5478,5564,5735,5811,5894,5976,6051,6129,6203,6289,6462,6541,6617,6703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a26e82d508f491fb2ba1b9b8b2ee8b33\\transformed\\biometric-1.1.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,253,370,509,656,787,917,1061,1162,1296,1440", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "154,248,365,504,651,782,912,1056,1157,1291,1435,1558"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3210,3314,3408,3525,3664,3811,3942,4072,4216,4317,4451,4595", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "3309,3403,3520,3659,3806,3937,4067,4211,4312,4446,4590,4713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "6294", "endColumns": "100", "endOffsets": "6390"}}]}]}