{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,154,230,313,387,463,545,625,703,783,857", "endColumns": "98,75,82,73,75,81,79,77,79,73,73", "endOffsets": "149,225,308,382,458,540,620,698,778,852,926"}, "to": {"startLines": "31,32,51,52,53,54,55,57,59,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3020,3119,5404,5487,5561,5637,5719,5919,6080,6261,6335", "endColumns": "98,75,82,73,75,81,79,77,79,73,73", "endOffsets": "3114,3190,5482,5556,5632,5714,5794,5992,6155,6330,6404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "6160", "endColumns": "100", "endOffsets": "6256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,5997", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,6075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,199,313,419,570,660,794,898,1009,1128,1257,1395,1522,1640,1771,1871,2019,2137,2263,2402,2522,2642,2735,2858,2940,3052,3148,3274", "endColumns": "143,113,105,150,89,133,103,110,118,128,137,126,117,130,99,147,117,125,138,119,119,92,122,81,111,95,125,95", "endOffsets": "194,308,414,565,655,789,893,1004,1123,1252,1390,1517,1635,1766,1866,2014,2132,2258,2397,2517,2637,2730,2853,2935,3047,3143,3269,3365"}, "to": {"startLines": "29,30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,56,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2762,2906,3195,3301,3452,3542,3676,3780,3891,4010,4139,4277,4404,4522,4653,4753,4901,5019,5145,5284,5799,6409,6502,6625,6707,6819,6915,7041", "endColumns": "143,113,105,150,89,133,103,110,118,128,137,126,117,130,99,147,117,125,138,119,119,92,122,81,111,95,125,95", "endOffsets": "2901,3015,3296,3447,3537,3671,3775,3886,4005,4134,4272,4399,4517,4648,4748,4896,5014,5140,5279,5399,5914,6497,6620,6702,6814,6910,7036,7132"}}]}]}