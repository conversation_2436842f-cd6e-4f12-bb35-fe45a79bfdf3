{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7494", "endColumns": "100", "endOffsets": "7590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,6764", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,6844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,248,339,422,495,564,646,714,781,857,940,1027,1107,1180,1264,1348,1425,1506,1588,1664,1741,1816,1909,1981,2065,2135", "endColumns": "77,114,90,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "128,243,334,417,490,559,641,709,776,852,935,1022,1102,1175,1259,1343,1420,1501,1583,1659,1736,1811,1904,1976,2060,2130,2211"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2820,3197,3312,3403,5784,5857,5926,6008,6076,6143,6219,6302,6389,6469,6680,6849,6933,7010,7091,7173,7249,7326,7401,7595,7667,7751,7821", "endColumns": "77,114,90,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "2893,3307,3398,3481,5852,5921,6003,6071,6138,6214,6297,6384,6464,6537,6759,6928,7005,7086,7168,7244,7321,7396,7489,7662,7746,7816,7897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,354,471,632,725,886,1005,1118,1239,1369,1493,1622,1740,1875,1976,2145,2272,2402,2529,2652,2790,2887,3018,3103,3225,3323,3458", "endColumns": "167,130,116,160,92,160,118,112,120,129,123,128,117,134,100,168,126,129,126,122,137,96,130,84,121,97,134,105", "endOffsets": "218,349,466,627,720,881,1000,1113,1234,1364,1488,1617,1735,1870,1971,2140,2267,2397,2524,2647,2785,2882,3013,3098,3220,3318,3453,3559"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2898,3066,3486,3603,3764,3857,4018,4137,4250,4371,4501,4625,4754,4872,5007,5108,5277,5404,5534,5661,6542,7902,7999,8130,8215,8337,8435,8570", "endColumns": "167,130,116,160,92,160,118,112,120,129,123,128,117,134,100,168,126,129,126,122,137,96,130,84,121,97,134,105", "endOffsets": "3061,3192,3598,3759,3852,4013,4132,4245,4366,4496,4620,4749,4867,5002,5103,5272,5399,5529,5656,5779,6675,7994,8125,8210,8332,8430,8565,8671"}}]}]}