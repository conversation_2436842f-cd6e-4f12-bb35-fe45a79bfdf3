import { simpleStorage } from './storage';
import * as Keychain from 'react-native-keychain';

export interface PinAuthResult {
  success: boolean;
  error?: string;
}

export interface PinSetupResult {
  success: boolean;
  error?: string;
}

class PinAuthService {
  private static instance: PinAuthService;
  private readonly PIN_KEY = 'autoflow_user_pin';
  private readonly PIN_ATTEMPTS_KEY = 'autoflow_pin_attempts';
  private readonly PIN_LOCKOUT_KEY = 'autoflow_pin_lockout';
  private readonly MAX_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 5 * 60 * 1000; // 5 minutes

  public static getInstance(): PinAuthService {
    if (!PinAuthService.instance) {
      PinAuthService.instance = new PinAuthService();
    }
    return PinAuthService.instance;
  }

  /**
   * Set up a new PIN for the user
   */
  async setupPin(userId: string, pin: string): Promise<PinSetupResult> {
    try {
      console.log('PinAuthService: Setting up PIN for user:', userId);

      // Validate PIN format
      if (!this.isValidPin(pin)) {
        return {
          success: false,
          error: 'PIN must be 4-6 digits'
        };
      }

      // Hash the PIN for security
      const hashedPin = await this.hashPin(pin);

      // Store PIN securely in Keychain
      await Keychain.setInternetCredentials(
        this.PIN_KEY,
        userId,
        hashedPin,
        {
          accessControl: Keychain.ACCESS_CONTROL.DEVICE_PASSCODE,
        }
      );

      // Reset attempts counter
      await simpleStorage.setItem(this.PIN_ATTEMPTS_KEY, '0');
      await simpleStorage.removeItem(this.PIN_LOCKOUT_KEY);

      console.log('PinAuthService: PIN setup successful');
      return { success: true };

    } catch (error) {
      console.error('PinAuthService: PIN setup failed:', error);
      return {
        success: false,
        error: 'Failed to set up PIN. Please try again.'
      };
    }
  }

  /**
   * Authenticate user with PIN
   */
  async authenticateWithPin(userId: string, pin: string): Promise<PinAuthResult> {
    try {
      console.log('PinAuthService: Authenticating with PIN for user:', userId);

      // Check if user is locked out
      const lockoutCheck = await this.checkLockout();
      if (!lockoutCheck.allowed) {
        return {
          success: false,
          error: lockoutCheck.message
        };
      }

      // Validate PIN format
      if (!this.isValidPin(pin)) {
        await this.incrementFailedAttempts();
        return {
          success: false,
          error: 'Invalid PIN format'
        };
      }

      // Get stored PIN
      const credentials = await Keychain.getInternetCredentials(this.PIN_KEY);
      if (!credentials || credentials.username !== userId) {
        await this.incrementFailedAttempts();
        return {
          success: false,
          error: 'PIN not found. Please set up PIN first.'
        };
      }

      // Hash entered PIN and compare
      const hashedPin = await this.hashPin(pin);
      if (hashedPin !== credentials.password) {
        await this.incrementFailedAttempts();
        const attempts = await this.getFailedAttempts();
        const remaining = this.MAX_ATTEMPTS - attempts;
        
        if (remaining <= 0) {
          await this.lockoutUser();
          return {
            success: false,
            error: 'Too many failed attempts. Please try again in 5 minutes.'
          };
        }

        return {
          success: false,
          error: `Incorrect PIN. ${remaining} attempts remaining.`
        };
      }

      // Success - reset attempts
      await simpleStorage.setItem(this.PIN_ATTEMPTS_KEY, '0');
      await simpleStorage.removeItem(this.PIN_LOCKOUT_KEY);

      console.log('PinAuthService: PIN authentication successful');
      return { success: true };

    } catch (error) {
      console.error('PinAuthService: PIN authentication failed:', error);
      return {
        success: false,
        error: 'Authentication failed. Please try again.'
      };
    }
  }

  /**
   * Check if user has PIN set up
   */
  async hasPinSetup(userId: string): Promise<boolean> {
    try {
      const credentials = await Keychain.getInternetCredentials(this.PIN_KEY);
      return credentials && credentials.username === userId;
    } catch (error) {
      console.error('PinAuthService: Error checking PIN setup:', error);
      return false;
    }
  }

  /**
   * Remove PIN (for logout or reset)
   */
  async removePin(): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(this.PIN_KEY);
      await simpleStorage.removeItem(this.PIN_ATTEMPTS_KEY);
      await simpleStorage.removeItem(this.PIN_LOCKOUT_KEY);
      console.log('PinAuthService: PIN removed successfully');
    } catch (error) {
      console.error('PinAuthService: Error removing PIN:', error);
    }
  }

  /**
   * Change existing PIN
   */
  async changePin(userId: string, oldPin: string, newPin: string): Promise<PinSetupResult> {
    try {
      // First verify old PIN
      const authResult = await this.authenticateWithPin(userId, oldPin);
      if (!authResult.success) {
        return {
          success: false,
          error: 'Current PIN is incorrect'
        };
      }

      // Set up new PIN
      return await this.setupPin(userId, newPin);
    } catch (error) {
      console.error('PinAuthService: Error changing PIN:', error);
      return {
        success: false,
        error: 'Failed to change PIN. Please try again.'
      };
    }
  }

  /**
   * Get lockout status
   */
  async getLockoutStatus(): Promise<{ isLockedOut: boolean; remainingTime?: number }> {
    try {
      const lockoutTime = await simpleStorage.getItem(this.PIN_LOCKOUT_KEY);
      if (!lockoutTime) {
        return { isLockedOut: false };
      }

      const lockoutTimestamp = parseInt(lockoutTime);
      const now = Date.now();
      const remainingTime = (lockoutTimestamp + this.LOCKOUT_DURATION) - now;

      if (remainingTime <= 0) {
        // Lockout expired
        await simpleStorage.removeItem(this.PIN_LOCKOUT_KEY);
        await simpleStorage.setItem(this.PIN_ATTEMPTS_KEY, '0');
        return { isLockedOut: false };
      }

      return {
        isLockedOut: true,
        remainingTime: Math.ceil(remainingTime / 1000) // seconds
      };
    } catch (error) {
      console.error('PinAuthService: Error checking lockout status:', error);
      return { isLockedOut: false };
    }
  }

  // Private helper methods
  private isValidPin(pin: string): boolean {
    return /^\d{4,6}$/.test(pin);
  }

  private async hashPin(pin: string): Promise<string> {
    // Simple hash - in production, use a proper crypto library
    let hash = 0;
    for (let i = 0; i < pin.length; i++) {
      const char = pin.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private async getFailedAttempts(): Promise<number> {
    try {
      const attempts = await simpleStorage.getItem(this.PIN_ATTEMPTS_KEY);
      return attempts ? parseInt(attempts) : 0;
    } catch (error) {
      return 0;
    }
  }

  private async incrementFailedAttempts(): Promise<void> {
    try {
      const current = await this.getFailedAttempts();
      await simpleStorage.setItem(this.PIN_ATTEMPTS_KEY, (current + 1).toString());
    } catch (error) {
      console.error('PinAuthService: Error incrementing failed attempts:', error);
    }
  }

  private async checkLockout(): Promise<{ allowed: boolean; message?: string }> {
    const lockoutStatus = await this.getLockoutStatus();
    if (lockoutStatus.isLockedOut) {
      const minutes = Math.ceil((lockoutStatus.remainingTime || 0) / 60);
      return {
        allowed: false,
        message: `Too many failed attempts. Please try again in ${minutes} minute${minutes !== 1 ? 's' : ''}.`
      };
    }
    return { allowed: true };
  }

  private async lockoutUser(): Promise<void> {
    try {
      await simpleStorage.setItem(this.PIN_LOCKOUT_KEY, Date.now().toString());
      console.log('PinAuthService: User locked out for', this.LOCKOUT_DURATION / 1000, 'seconds');
    } catch (error) {
      console.error('PinAuthService: Error setting lockout:', error);
    }
  }
}

export const pinAuthService = PinAuthService.getInstance();
