{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,329,438,594,687,848,969,1085,1208,1340,1471,1599,1729,1863,1964,2125,2246,2381,2514,2637,2764,2861,2998,3099,3240,3341,3482", "endColumns": "156,116,108,155,92,160,120,115,122,131,130,127,129,133,100,160,120,134,132,122,126,96,136,100,140,100,140,108", "endOffsets": "207,324,433,589,682,843,964,1080,1203,1335,1466,1594,1724,1858,1959,2120,2241,2376,2509,2632,2759,2856,2993,3094,3235,3336,3477,3586"}, "to": {"startLines": "30,31,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,62,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2904,3061,3352,3461,3617,3710,3871,3992,4108,4231,4363,4494,4622,4752,4886,4987,5148,5269,5404,5537,6416,7760,7857,7994,8095,8236,8337,8478", "endColumns": "156,116,108,155,92,160,120,115,122,131,130,127,129,133,100,160,120,134,132,122,126,96,136,100,140,100,140,108", "endOffsets": "3056,3173,3456,3612,3705,3866,3987,4103,4226,4358,4489,4617,4747,4881,4982,5143,5264,5399,5532,5655,6538,7852,7989,8090,8231,8332,8473,8582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,218,303,377,446,528,595,662,740,820,904,987,1059,1140,1227,1303,1386,1468,1547,1625,1701,1789,1862,1941,2011", "endColumns": "73,88,84,73,68,81,66,66,77,79,83,82,71,80,86,75,82,81,78,77,75,87,72,78,69,76", "endOffsets": "124,213,298,372,441,523,590,657,735,815,899,982,1054,1135,1222,1298,1381,1463,1542,1620,1696,1784,1857,1936,2006,2083"}, "to": {"startLines": "29,32,33,52,53,54,55,56,57,58,59,60,61,63,65,66,67,68,69,70,71,72,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2830,3178,3267,5660,5734,5803,5885,5952,6019,6097,6177,6261,6344,6543,6711,6798,6874,6957,7039,7118,7196,7272,7461,7534,7613,7683", "endColumns": "73,88,84,73,68,81,66,66,77,79,83,82,71,80,86,75,82,81,78,77,75,87,72,78,69,76", "endOffsets": "2899,3262,3347,5729,5798,5880,5947,6014,6092,6172,6256,6339,6411,6619,6793,6869,6952,7034,7113,7191,7267,7355,7529,7608,7678,7755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,6624", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,6706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "7360", "endColumns": "100", "endOffsets": "7456"}}]}]}