{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,124,207,277,344,416", "endColumns": "68,82,69,66,71,71", "endOffsets": "119,202,272,339,411,483"}, "to": {"startLines": "49,50,51,52,55,56", "startColumns": "4,4,4,4,4,4", "startOffsets": "5482,5551,5634,5704,5980,6052", "endColumns": "68,82,69,66,71,71", "endOffsets": "5546,5629,5699,5766,6047,6119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,5898", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,5975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "6124", "endColumns": "100", "endOffsets": "6220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,351,460,615,705,853,965,1082,1202,1349,1478,1628,1753,1906,2005,2161,2290,2432,2591,2720,2847,2962,3108,3212,3352,3452,3588", "endColumns": "167,127,108,154,89,147,111,116,119,146,128,149,124,152,98,155,128,141,158,128,126,114,145,103,139,99,135,103", "endOffsets": "218,346,455,610,700,848,960,1077,1197,1344,1473,1623,1748,1901,2000,2156,2285,2427,2586,2715,2842,2957,3103,3207,3347,3447,3583,3687"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,53,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2817,2985,3113,3222,3377,3467,3615,3727,3844,3964,4111,4240,4390,4515,4668,4767,4923,5052,5194,5353,5771,6225,6340,6486,6590,6730,6830,6966", "endColumns": "167,127,108,154,89,147,111,116,119,146,128,149,124,152,98,155,128,141,158,128,126,114,145,103,139,99,135,103", "endOffsets": "2980,3108,3217,3372,3462,3610,3722,3839,3959,4106,4235,4385,4510,4663,4762,4918,5047,5189,5348,5477,5893,6335,6481,6585,6725,6825,6961,7065"}}]}]}