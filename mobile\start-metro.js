const { execSync } = require('child_process');
const path = require('path');

console.log('Starting Metro bundler...');
console.log('Current directory:', process.cwd());

try {
  // Start Metro with explicit configuration
  execSync('npx metro start --host 0.0.0.0 --port 8081 --reset-cache', {
    stdio: 'inherit',
    cwd: process.cwd()
  });
} catch (error) {
  console.error('Error starting Metro:', error.message);
  process.exit(1);
}
