import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const results: any = {
      timestamp: new Date().toISOString(),
      tables: {}
    };

    // Check parts table
    try {
      const { data: parts, error: partsError, count: partsCount } = await supabase
        .from('parts')
        .select('id, title, is_active', { count: 'exact' })
        .limit(5);
      
      results.tables.parts = {
        exists: !partsError,
        error: partsError?.message || null,
        count: partsCount || 0,
        sample: parts || []
      };
    } catch (error: any) {
      results.tables.parts = {
        exists: false,
        error: error.message,
        count: 0,
        sample: []
      };
    }

    // Check categories table
    try {
      const { data: categories, error: categoriesError, count: categoriesCount } = await supabase
        .from('categories')
        .select('id, name, is_active', { count: 'exact' })
        .limit(5);
      
      results.tables.categories = {
        exists: !categoriesError,
        error: categoriesError?.message || null,
        count: categoriesCount || 0,
        sample: categories || []
      };
    } catch (error: any) {
      results.tables.categories = {
        exists: false,
        error: error.message,
        count: 0,
        sample: []
      };
    }

    // Check car_brands table
    try {
      const { data: brands, error: brandsError, count: brandsCount } = await supabase
        .from('car_brands')
        .select('brand_id, brand_name', { count: 'exact' })
        .limit(5);
      
      results.tables.car_brands = {
        exists: !brandsError,
        error: brandsError?.message || null,
        count: brandsCount || 0,
        sample: brands || []
      };
    } catch (error: any) {
      results.tables.car_brands = {
        exists: false,
        error: error.message,
        count: 0,
        sample: []
      };
    }

    // Check car_models table
    try {
      const { data: models, error: modelsError, count: modelsCount } = await supabase
        .from('car_models')
        .select('id, model_name', { count: 'exact' })
        .limit(5);
      
      results.tables.car_models = {
        exists: !modelsError,
        error: modelsError?.message || null,
        count: modelsCount || 0,
        sample: models || []
      };
    } catch (error: any) {
      results.tables.car_models = {
        exists: false,
        error: error.message,
        count: 0,
        sample: []
      };
    }

    // Summary
    const hasData = Object.values(results.tables).some((table: any) => table.count > 0);
    results.summary = {
      hasData,
      totalTables: Object.keys(results.tables).length,
      tablesWithData: Object.values(results.tables).filter((table: any) => table.count > 0).length,
      sitemapExpectation: hasData ? 'Should contain data' : 'Expected to be empty'
    };

    return NextResponse.json(results, { status: 200 });

  } catch (error: any) {
    return NextResponse.json(
      { 
        error: 'Database check failed', 
        details: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
