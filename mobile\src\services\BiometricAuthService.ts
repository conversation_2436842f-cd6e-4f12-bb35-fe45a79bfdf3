import ReactNativeBiometrics, { BiometryTypes } from '@sbaiahmed1/react-native-biometrics';
import Keychain from 'react-native-keychain';
import { 
  BiometricSupportInfo, 
  BiometricEnrollmentResult, 
  BiometricAuthResult,
  BiometricErrorCodes 
} from '../types/biometric.types';
import { apiService } from './ApiService';
import { securityService } from './SecurityService';
import { BIOMETRIC_CONSTANTS } from '../utils/biometric-constants';

class BiometricAuthService {
  private static instance: BiometricAuthService;
  private rnBiometrics: any;

  private constructor() {
    this.rnBiometrics = new ReactNativeBiometrics({
      allowDeviceCredentials: true, // Enable PIN/password fallback
    });
  }

  public static getInstance(): BiometricAuthService {
    if (!BiometricAuthService.instance) {
      BiometricAuthService.instance = new BiometricAuthService();
    }
    return BiometricAuthService.instance;
  }

  /**
   * Check if biometric authentication is supported and available
   * This is the first method that should be called before any biometric operations
   */
  async checkBiometricSupport(): Promise<BiometricSupportInfo> {
    try {
      // Critical security check - disable biometrics on rooted devices
      const isRooted = await securityService.isDeviceRooted();
      if (isRooted) {
        return {
          isSupported: false,
          biometryType: null,
          error: BIOMETRIC_CONSTANTS.MESSAGES.ROOT_DETECTED
        };
      }

      const { available, biometryType, error } = await this.rnBiometrics.isSensorAvailable();
      
      console.log('BiometricAuthService: Sensor check result:', { available, biometryType, error });

      if (!available) {
        return {
          isSupported: false,
          biometryType: null,
          error: error || 'Biometric sensor not available'
        };
      }

      return {
        isSupported: true,
        biometryType: biometryType || 'Unknown',
        error: undefined
      };
    } catch (error) {
      console.error('BiometricAuthService: Error checking biometric support:', error);
      return {
        isSupported: false,
        biometryType: null,
        error: 'Failed to check biometric support'
      };
    }
  }

  /**
   * Check if biometric keys already exist on the device
   */
  async doesBiometricKeyExist(): Promise<boolean> {
    try {
      const { keysExist } = await this.rnBiometrics.biometricKeysExist();
      console.log('BiometricAuthService: Keys exist:', keysExist);
      return keysExist;
    } catch (error) {
      console.error('BiometricAuthService: Error checking key existence:', error);
      return false;
    }
  }

  /**
   * Enroll biometric authentication for a user
   * This generates a new key pair and registers the public key with the backend
   */
  async enrollBiometrics(userId: string): Promise<BiometricEnrollmentResult> {
    try {
      console.log('BiometricAuthService: Starting biometric enrollment for user:', userId);

      // Security check
      const isRooted = await securityService.isDeviceRooted();
      if (isRooted) {
        return {
          success: false,
          error: BIOMETRIC_CONSTANTS.MESSAGES.ROOT_DETECTED
        };
      }

      // Check if biometrics are supported
      const supportInfo = await this.checkBiometricSupport();
      if (!supportInfo.isSupported) {
        return {
          success: false,
          error: supportInfo.error || 'Biometric authentication not supported'
        };
      }

      // Delete any existing keys first (cleanup)
      try {
        await this.rnBiometrics.deleteKeys();
        console.log('BiometricAuthService: Cleaned up existing keys');
      } catch (cleanupError) {
        console.warn('BiometricAuthService: No existing keys to cleanup:', cleanupError);
      }

      // Generate new key pair with biometric invalidation
      console.log('BiometricAuthService: Creating new biometric keys...');
      const { publicKey } = await this.rnBiometrics.createKeys({
        promptMessage: 'Set up biometric authentication',
        cancelButtonText: BIOMETRIC_CONSTANTS.CANCEL_BUTTON,
        // Critical security setting - invalidate keys when biometrics change
        invalidateOnEnrollment: true,
      });

      if (!publicKey) {
        throw new Error('Failed to generate public key');
      }

      console.log('BiometricAuthService: Keys generated successfully, registering with backend...');

      // Register public key with backend
      await apiService.registerBiometricKey(userId, publicKey);

      console.log('BiometricAuthService: Biometric enrollment completed successfully');

      return {
        success: true,
        publicKey
      };

    } catch (error: any) {
      console.error('BiometricAuthService: Enrollment error:', error);
      
      // Handle specific biometric errors
      const errorCode = this.mapErrorToCode(error);
      const errorMessage = this.getErrorMessage(errorCode);

      // Cleanup on failure
      try {
        await this.rnBiometrics.deleteKeys();
      } catch (cleanupError) {
        console.warn('BiometricAuthService: Failed to cleanup keys after enrollment error:', cleanupError);
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Map native errors to standardized error codes
   */
  private mapErrorToCode(error: any): string {
    if (!error) return BiometricErrorCodes.ERROR_UNABLE_TO_PROCESS;
    
    const errorString = error.toString().toLowerCase();
    
    if (errorString.includes('lockout') || errorString.includes('too many attempts')) {
      return BiometricErrorCodes.ERROR_LOCKOUT;
    }
    if (errorString.includes('no biometrics') || errorString.includes('not enrolled')) {
      return BiometricErrorCodes.ERROR_NO_BIOMETRICS;
    }
    if (errorString.includes('hardware unavailable')) {
      return BiometricErrorCodes.ERROR_HW_UNAVAILABLE;
    }
    if (errorString.includes('hardware not present') || errorString.includes('no hardware')) {
      return BiometricErrorCodes.ERROR_HW_NOT_PRESENT;
    }
    if (errorString.includes('user canceled') || errorString.includes('cancelled')) {
      return BiometricErrorCodes.ERROR_USER_CANCELED;
    }
    if (this.isKeyInvalidatedError(error)) {
      return BiometricErrorCodes.ERROR_KEY_PERMANENTLY_INVALIDATED;
    }
    if (errorString.includes('timeout')) {
      return BiometricErrorCodes.ERROR_TIMEOUT;
    }
    
    return BiometricErrorCodes.ERROR_UNABLE_TO_PROCESS;
  }

  /**
   * Get user-friendly error message for error code
   */
  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case BiometricErrorCodes.ERROR_LOCKOUT:
        return BIOMETRIC_CONSTANTS.MESSAGES.LOCKOUT;
      case BiometricErrorCodes.ERROR_NO_BIOMETRICS:
        return BIOMETRIC_CONSTANTS.MESSAGES.NO_BIOMETRICS;
      case BiometricErrorCodes.ERROR_HW_UNAVAILABLE:
        return BIOMETRIC_CONSTANTS.MESSAGES.HW_UNAVAILABLE;
      case BiometricErrorCodes.ERROR_HW_NOT_PRESENT:
        return BIOMETRIC_CONSTANTS.MESSAGES.HW_NOT_PRESENT;
      case BiometricErrorCodes.ERROR_KEY_PERMANENTLY_INVALIDATED:
        return BIOMETRIC_CONSTANTS.MESSAGES.KEY_INVALIDATED;
      case BiometricErrorCodes.ERROR_TIMEOUT:
        return BIOMETRIC_CONSTANTS.MESSAGES.TIMEOUT;
      case BiometricErrorCodes.ERROR_USER_CANCELED:
        return ''; // Don't show error for user cancellation
      default:
        return BIOMETRIC_CONSTANTS.MESSAGES.GENERIC_ERROR;
    }
  }

  /**
   * Check if error indicates key permanently invalidated
   */
  private isKeyInvalidatedError(error: any): boolean {
    if (!error) return false;

    const errorString = error.toString().toLowerCase();
    return (
      errorString.includes('keypermanentlyinvalidated') ||
      errorString.includes('key_permanently_invalidated') ||
      errorString.includes('biometric enrollment has changed') ||
      errorString.includes('invalidated by biometric enrollment')
    );
  }

  /**
   * Authenticate user using biometric signature verification
   * This implements the asymmetric cryptography model with challenge-response
   */
  async authenticate(userId: string): Promise<BiometricAuthResult> {
    try {
      console.log('BiometricAuthService: Starting biometric authentication for user:', userId);

      // Security check
      const isRooted = await securityService.isDeviceRooted();
      if (isRooted) {
        return {
          success: false,
          error: BIOMETRIC_CONSTANTS.MESSAGES.ROOT_DETECTED,
          errorCode: 'ROOT_DETECTED'
        };
      }

      // Check if keys exist
      const keysExist = await this.doesBiometricKeyExist();
      if (!keysExist) {
        console.log('BiometricAuthService: No biometric keys found');
        return {
          success: false,
          error: 'Biometric authentication not set up',
          errorCode: 'NO_KEYS'
        };
      }

      // Step 1: Get challenge from server
      console.log('BiometricAuthService: Requesting challenge from server...');
      const challengeResponse = await apiService.getBiometricChallenge(userId);
      const { challenge } = challengeResponse;

      // Step 2: Create signature using biometric authentication
      console.log('BiometricAuthService: Creating biometric signature...');
      const { success, signature, error } = await this.rnBiometrics.createSignature({
        promptMessage: BIOMETRIC_CONSTANTS.PROMPT_TITLE,
        payload: challenge,
        cancelButtonText: BIOMETRIC_CONSTANTS.CANCEL_BUTTON,
        fallbackPromptMessage: 'Use your device PIN or password',
      });

      if (!success || !signature) {
        console.log('BiometricAuthService: Biometric signature creation failed:', error);

        // Handle key permanently invalidated error
        if (this.isKeyInvalidatedError(error)) {
          console.log('BiometricAuthService: Key permanently invalidated, triggering recovery flow');
          await this.handleKeyInvalidation(userId);
          return {
            success: false,
            error: BIOMETRIC_CONSTANTS.MESSAGES.KEY_INVALIDATED,
            errorCode: BiometricErrorCodes.ERROR_KEY_PERMANENTLY_INVALIDATED
          };
        }

        const errorCode = this.mapErrorToCode(error);
        return {
          success: false,
          error: this.getErrorMessage(errorCode),
          errorCode
        };
      }

      // Step 3: Verify signature with backend
      console.log('BiometricAuthService: Verifying signature with backend...');
      const verificationResponse = await apiService.verifyBiometricSignature({
        userId,
        challenge,
        signature
      });

      if (!verificationResponse.success || !verificationResponse.token) {
        return {
          success: false,
          error: 'Authentication verification failed',
          errorCode: 'VERIFICATION_FAILED'
        };
      }

      // Step 4: Store session token securely
      console.log('BiometricAuthService: Storing session token...');
      await Keychain.setInternetCredentials(
        'autoflow_session',
        userId,
        verificationResponse.token,
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET,
        }
      );

      console.log('BiometricAuthService: Authentication completed successfully');

      return {
        success: true,
        token: verificationResponse.token
      };

    } catch (error: any) {
      console.error('BiometricAuthService: Authentication error:', error);

      const errorCode = this.mapErrorToCode(error);
      return {
        success: false,
        error: this.getErrorMessage(errorCode),
        errorCode
      };
    }
  }

  /**
   * Disable biometric authentication and cleanup all keys
   */
  async disableBiometrics(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('BiometricAuthService: Disabling biometric authentication for user:', userId);

      // Delete local keys
      await this.rnBiometrics.deleteKeys();
      console.log('BiometricAuthService: Local keys deleted');

      // Delete public key from backend
      await apiService.deleteBiometricKey(userId);
      console.log('BiometricAuthService: Backend key deleted');

      // Clear stored session tokens
      try {
        await Keychain.resetInternetCredentials('autoflow_session');
      } catch (keychainError) {
        console.warn('BiometricAuthService: No session credentials to clear:', keychainError);
      }

      return { success: true };

    } catch (error: any) {
      console.error('BiometricAuthService: Error disabling biometrics:', error);
      return {
        success: false,
        error: 'Failed to disable biometric authentication'
      };
    }
  }

  /**
   * Handle key permanently invalidated scenario
   * This is critical for security when user's biometrics change
   */
  private async handleKeyInvalidation(userId: string): Promise<void> {
    try {
      console.log('BiometricAuthService: Handling key invalidation recovery...');

      // Delete local keys
      await this.rnBiometrics.deleteKeys();

      // Delete backend public key
      await apiService.deleteBiometricKey(userId);

      // Clear session tokens
      try {
        await Keychain.resetInternetCredentials('autoflow_session');
      } catch (error) {
        console.warn('BiometricAuthService: No session credentials to clear:', error);
      }

      console.log('BiometricAuthService: Key invalidation recovery completed');
    } catch (error) {
      console.error('BiometricAuthService: Error during key invalidation recovery:', error);
    }
  }
}

// Export singleton instance
export const biometricAuthService = BiometricAuthService.getInstance();
