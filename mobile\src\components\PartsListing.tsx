import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList, RefreshControl, ActivityIndicator } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY } from '../constants/theme';
import PartCard from './PartCard';

interface Part {
  id: number;
  title: string;
  price: string;
  discountedPrice?: string;
  condition: string;
  stock: number;
  thumbnailUrl?: string | null;
}

interface Props {
  parts: Part[];
  onPartPress: (partId: number) => void;
  onCallPress: (partId: number) => void;
  onWhatsAppPress: (partId: number) => void;
  title?: string;
  subtitle?: string;
  loading?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
}

type ViewMode = 'list' | 'grid';

const PartsListing: React.FC<Props> = ({
  parts,
  onPartPress,
  onCallPress,
  onWhatsAppPress,
  title = "Auto Parts",
  subtitle = "Browse our extensive catalog",
  loading = false,
  onRefresh,
  onLoadMore
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('list'); // Default to list view

  const renderPartItem = ({ item }: { item: Part }) => (
    <PartCard
      key={item.id}
      id={item.id}
      title={item.title}
      price={item.price}
      discountedPrice={item.discountedPrice}
      condition={item.condition}
      stock={item.stock}
      thumbnailUrl={item.thumbnailUrl}
      onPress={() => onPartPress(item.id)}
      onCallPress={() => onCallPress(item.id)}
      onWhatsAppPress={() => onWhatsAppPress(item.id)}
      isGridView={viewMode === 'grid'}
    />
  );

  return (
    <View style={styles.container}>
      {/* Header with View Toggle */}
      <View style={styles.header}>
        <View style={styles.headerText}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>{subtitle}</Text>
        </View>
        
        {/* View Mode Toggle */}
        <View style={styles.viewToggle}>
          <TouchableOpacity
            style={[styles.toggleButton, viewMode === 'list' && styles.activeToggle]}
            onPress={() => setViewMode('list')}
          >
            <Text style={[styles.toggleText, viewMode === 'list' && styles.activeToggleText]}>
              ☰
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.toggleButton, viewMode === 'grid' && styles.activeToggle]}
            onPress={() => setViewMode('grid')}
          >
            <Text style={[styles.toggleText, viewMode === 'grid' && styles.activeToggleText]}>
              ⊞
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Loading Indicator */}
      {loading && parts.length === 0 && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.PRIMARY} />
          <Text style={styles.loadingText}>Loading parts...</Text>
        </View>
      )}

      {/* Empty State */}
      {!loading && parts.length === 0 && (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No parts found</Text>
          <Text style={styles.emptySubtext}>Try adjusting your search or check back later</Text>
        </View>
      )}

      {/* Parts List/Grid */}
      {parts.length > 0 && (
        viewMode === 'list' ? (
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            refreshControl={
              onRefresh ? (
                <RefreshControl
                  refreshing={loading}
                  onRefresh={onRefresh}
                  colors={[COLORS.PRIMARY]}
                  tintColor={COLORS.PRIMARY}
                />
              ) : undefined
            }
          >
            <View style={styles.listContainer}>
              {parts.map((part) => (
                <PartCard
                  key={part.id}
                  id={part.id}
                  title={part.title}
                  price={part.price}
                  discountedPrice={part.discountedPrice}
                  condition={part.condition}
                  stock={part.stock}
                  thumbnailUrl={part.thumbnailUrl}
                  onPress={() => onPartPress(part.id)}
                  onCallPress={() => onCallPress(part.id)}
                  onWhatsAppPress={() => onWhatsAppPress(part.id)}
                  isGridView={false}
                />
              ))}

              {/* Load More Button */}
              {onLoadMore && (
                <TouchableOpacity style={styles.loadMoreButton} onPress={onLoadMore}>
                  <Text style={styles.loadMoreText}>Load More Parts</Text>
                </TouchableOpacity>
              )}
            </View>
          </ScrollView>
        ) : (
          <FlatList
            data={parts}
            renderItem={renderPartItem}
            numColumns={2}
            key={viewMode} // Force re-render when switching modes
            contentContainerStyle={styles.gridContainer}
            columnWrapperStyle={styles.gridRow}
            showsVerticalScrollIndicator={false}
            refreshControl={
              onRefresh ? (
                <RefreshControl
                  refreshing={loading}
                  onRefresh={onRefresh}
                  colors={[COLORS.PRIMARY]}
                  tintColor={COLORS.PRIMARY}
                />
              ) : undefined
            }
            onEndReached={onLoadMore}
            onEndReachedThreshold={0.1}
            ListFooterComponent={
              onLoadMore ? (
                <View style={styles.loadMoreContainer}>
                  <TouchableOpacity style={styles.loadMoreButton} onPress={onLoadMore}>
                    <Text style={styles.loadMoreText}>Load More Parts</Text>
                  </TouchableOpacity>
                </View>
              ) : null
            }
          />
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.lg,
    backgroundColor: COLORS.BACKGROUND,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_SECONDARY,
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: COLORS.SURFACE,
    borderRadius: 8,
    padding: 2,
  },
  toggleButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 6,
    minWidth: 40,
    alignItems: 'center',
  },
  activeToggle: {
    backgroundColor: COLORS.PRIMARY,
  },
  toggleText: {
    fontSize: TYPOGRAPHY.sizes.lg,
    color: COLORS.TEXT_SECONDARY,
  },
  activeToggleText: {
    color: COLORS.WHITE,
  },
  scrollView: {
    flex: 1,
  },
  listContainer: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.xl,
  },
  gridContainer: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.xl,
  },
  gridRow: {
    justifyContent: 'space-between',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl * 2,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_SECONDARY,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl * 2,
    paddingHorizontal: SPACING.lg,
  },
  emptyText: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.sm,
  },
  emptySubtext: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  loadMoreContainer: {
    paddingVertical: SPACING.lg,
    alignItems: 'center',
  },
  loadMoreButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: 8,
    marginVertical: SPACING.md,
  },
  loadMoreText: {
    color: COLORS.WHITE,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});

export default PartsListing;
