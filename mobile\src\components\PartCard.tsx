import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY, SHADOWS } from '../constants/theme';

interface PartCardProps {
  id: number;
  title: string;
  price: string;
  discountedPrice?: string;
  condition: string;
  stock: number;
  thumbnailUrl?: string;
  onPress: () => void;
  onCallPress?: () => void;
  onWhatsAppPress?: () => void;
  isGridView?: boolean;
}

const PartCard: React.FC<PartCardProps> = ({
  id,
  title,
  price,
  discountedPrice,
  condition,
  stock,
  thumbnailUrl,
  onPress,
  onCallPress,
  onWhatsAppPress,
  isGridView = false,
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, isGridView && styles.gridContainer]}
      onPress={onPress}
    >
      {/* Image */}
      <View style={[styles.imageContainer, isGridView && styles.gridImageContainer]}>
        {thumbnailUrl ? (
          <Image source={{ uri: thumbnailUrl }} style={styles.image} />
        ) : (
          <View style={styles.placeholderImage}>
            <Text style={[styles.placeholderText, isGridView && styles.gridPlaceholderText]}>🔧</Text>
          </View>
        )}
      </View>

      {/* Content */}
      <View style={[styles.content, isGridView && styles.gridContent]}>
        {/* Title */}
        <Text style={[styles.title, isGridView && styles.gridTitle]} numberOfLines={isGridView ? 3 : 2}>
          {title}
        </Text>

        {/* Price */}
        <View style={styles.priceContainer}>
          <Text style={styles.price}>{price}</Text>
          {discountedPrice && (
            <Text style={styles.originalPrice}>{discountedPrice}</Text>
          )}
        </View>

        {/* Details */}
        <View style={styles.details}>
          <Text style={styles.condition}>Condition: {condition}</Text>
          <Text style={[styles.stock, stock === 0 && styles.outOfStock]}>
            {stock === 0 ? 'Out of Stock' : `${stock} in stock`}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={[styles.actionButtons, isGridView && styles.gridActionButtons]}>
          {isGridView ? (
            // Compact buttons for grid view
            <View style={styles.gridButtonsContainer}>
              <TouchableOpacity style={styles.gridViewButton} onPress={onPress}>
                <Text style={styles.gridViewButtonText}>View</Text>
              </TouchableOpacity>
              <View style={styles.gridContactButtons}>
                {onCallPress && (
                  <TouchableOpacity style={styles.gridCallButton} onPress={onCallPress}>
                    <Text style={styles.contactButtonText}>📞</Text>
                  </TouchableOpacity>
                )}
                {onWhatsAppPress && (
                  <TouchableOpacity style={styles.gridWhatsappButton} onPress={onWhatsAppPress}>
                    <Text style={styles.contactButtonText}>💬</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          ) : (
            // Full buttons for list view
            <>
              <TouchableOpacity style={styles.viewButton} onPress={onPress}>
                <Text style={styles.viewButtonText}>View Details</Text>
              </TouchableOpacity>

              <View style={styles.contactButtons}>
                {onCallPress && (
                  <TouchableOpacity style={styles.callButton} onPress={onCallPress}>
                    <Text style={styles.contactButtonText}>📞</Text>
                  </TouchableOpacity>
                )}
                {onWhatsAppPress && (
                  <TouchableOpacity style={styles.whatsappButton} onPress={onWhatsAppPress}>
                    <Text style={styles.contactButtonText}>💬</Text>
                  </TouchableOpacity>
                )}
              </View>
            </>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 8,
    marginBottom: SPACING.md,
    ...SHADOWS.sm,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  imageContainer: {
    height: 120,
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  image: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    resizeMode: 'cover',
  },
  placeholderImage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  placeholderText: {
    fontSize: 32,
  },
  content: {
    padding: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.sm,
    lineHeight: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  price: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.PRIMARY,
    marginRight: SPACING.sm,
  },
  originalPrice: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.TEXT_SECONDARY,
    textDecorationLine: 'line-through',
  },
  details: {
    marginBottom: SPACING.md,
  },
  condition: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 2,
  },
  stock: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.SUCCESS,
  },
  outOfStock: {
    color: COLORS.ERROR,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  viewButton: {
    flex: 1,
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 6,
    marginRight: SPACING.sm,
  },
  viewButtonText: {
    color: COLORS.BACKGROUND,
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    textAlign: 'center',
  },
  contactButtons: {
    flexDirection: 'row',
  },
  callButton: {
    backgroundColor: COLORS.INFO,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    borderRadius: 6,
    marginRight: SPACING.xs,
  },
  whatsappButton: {
    backgroundColor: COLORS.SUCCESS,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    borderRadius: 6,
  },
  contactButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
  },
  // Grid view styles
  gridContainer: {
    width: '48%', // Two columns with some spacing
    marginBottom: SPACING.md,
  },
  gridImageContainer: {
    height: 100, // Smaller image for grid
  },
  gridPlaceholderText: {
    fontSize: 24, // Smaller icon for grid
  },
  gridContent: {
    padding: SPACING.sm, // Less padding for grid
  },
  gridTitle: {
    fontSize: TYPOGRAPHY.sizes.sm, // Smaller title for grid
    lineHeight: 16,
  },
  gridActionButtons: {
    flexDirection: 'column',
  },
  gridButtonsContainer: {
    width: '100%',
  },
  gridViewButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 4,
    marginBottom: SPACING.xs,
  },
  gridViewButtonText: {
    color: COLORS.BACKGROUND,
    fontSize: TYPOGRAPHY.sizes.xs,
    fontWeight: TYPOGRAPHY.weights.medium,
    textAlign: 'center',
  },
  gridContactButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  gridCallButton: {
    backgroundColor: COLORS.INFO,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 4,
    flex: 1,
    marginRight: SPACING.xs,
    alignItems: 'center',
  },
  gridWhatsappButton: {
    backgroundColor: COLORS.SUCCESS,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 4,
    flex: 1,
    alignItems: 'center',
  },
});

export default PartCard;
