'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Edit, Tag } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { CategoryWithChildren, CategoryFormData } from '../types';
import { useForm, SubmitHandler } from 'react-hook-form';
import Tabs, { Tab } from '@/app/components/ui/Tabs';
import AttributesTab from './AttributesTab';

interface EditCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  category: CategoryWithChildren;
  onSuccess: (updatedCategory?: any) => void;
  initialActiveTab?: string | number;
}

const EditCategoryModal: React.FC<EditCategoryModalProps> = ({
  isOpen,
  onClose,
  category,
  onSuccess,
  initialActiveTab
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string | number>(initialActiveTab || 'general');

  // Define tabs
  const modalTabs: Tab[] = [
    { id: 'general', label: 'General Info' },
    { id: 'attributes', label: 'Attributes' },
  ];

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue
  } = useForm<CategoryFormData>();

  // Initialize form with category data
  useEffect(() => {
    if (category) {
      setValue('label', category.label);
      setValue('href', category.href);
      setValue('icon', category.icon || '');
      setValue('library', category.library || '');
      setValue('parent_category_id', category.parent_category_id || null);
      setValue('isActive', category.isActive ?? false);
      // Fix for requirePartNumber - use nullish coalescing to only default to true if the value is null or undefined
      setValue('requirePartNumber', category.requirePartNumber ?? true);
      setValue('isEnginePart', category.isEnginePart ?? false);
    }
  }, [category, setValue]);

  const supabase = createClient();

  const onSubmit: SubmitHandler<CategoryFormData> = async (data) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Prepare the updated category data
      const updatedCategory = {
        id: category.id,
        label: data.label,
        href: data.href,
        icon: data.icon || null,
        library: data.library || null,
        parent_category_id: data.parent_category_id || null,
        isActive: data.isActive,
        requirePartNumber: data.requirePartNumber,
        isEnginePart: data.isEnginePart
      };

      const { error: updateError } = await supabase
        .from('car_part_categories')
        .update({
          label: data.label,
          href: data.href,
          icon: data.icon || null,
          library: data.library || null,
          parent_category_id: data.parent_category_id || null,
          isActive: data.isActive,
          requirePartNumber: data.requirePartNumber,
          isEnginePart: data.isEnginePart
        })
        .eq('id', category.id);

      if (updateError) throw updateError;

      // Success - pass the updated category data to the onSuccess callback
      onSuccess(updatedCategory);
      onClose();
    } catch (err: any) {
      console.error('Error updating category:', err);
      setError(err.message || 'Failed to update category');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      y: 50,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  // Get all categories from Supabase to show in parent selection dropdown
  const [allCategories, setAllCategories] = useState<CategoryWithChildren[]>([]);

  useEffect(() => {
    const fetchAllCategories = async () => {
      try {
        const { data, error } = await supabase
          .from('car_part_categories')
          .select('*')
          .order('label');

        if (error) throw error;

        // Process categories to create a hierarchical structure
        const processedCategories = processCategories(data || []);
        setAllCategories(processedCategories);
      } catch (err) {
        console.error('Error fetching categories:', err);
      }
    };

    fetchAllCategories();
  }, [supabase]);

  // Process categories to create a hierarchical structure
  const processCategories = (data: any[]): CategoryWithChildren[] => {
    // Create a map for quick lookup
    const categoryMap = new Map();
    data.forEach(category => {
      categoryMap.set(category.id, {
        ...category,
        children: []
      });
    });

    // Build the tree structure
    const rootCategories: CategoryWithChildren[] = [];
    data.forEach(category => {
      if (category.parent_category_id) {
        const parent = categoryMap.get(category.parent_category_id);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      } else {
        rootCategories.push(categoryMap.get(category.id));
      }
    });

    return rootCategories;
  };

  // Flatten categories for select dropdown with improved formatting
  const flattenCategories = (cats: CategoryWithChildren[], depth = 0, path = ''): { id: number; label: string; depth: number; fullPath: string }[] => {
    let result: { id: number; label: string; depth: number; fullPath: string }[] = [];

    cats.forEach(cat => {
      // Skip the current category to prevent circular references
      if (cat.id === category.id) return;

      const currentPath = path ? `${path} > ${cat.label}` : cat.label;
      result.push({ id: cat.id, label: cat.label, depth, fullPath: currentPath });

      if (cat.children && cat.children.length > 0) {
        result = [...result, ...flattenCategories(cat.children, depth + 1, currentPath)];
      }
    });

    return result;
  };

  const flatCategories = flattenCategories(allCategories);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div className="flex items-center">
                <Edit className="w-6 h-6 text-teal-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-800">Edit Category</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Add tabs navigation */}
            <div className="px-6 pt-2">
              <Tabs
                tabs={modalTabs}
                activeTabId={activeTab}
                onTabChange={setActiveTab}
              />
            </div>

            {error && (
              <div className="px-6 pt-4 pb-0">
                <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              </div>
            )}

            {/* Tab content containers */}
            <div className="p-6">
              {/* General Info Tab */}
              {activeTab === 'general' && (
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  <div>
                    <label htmlFor="label" className="block text-sm font-medium text-gray-700 mb-1">
                      Category Name *
                    </label>
                    <input
                      id="label"
                      type="text"
                      {...register('label', { required: 'Category name is required' })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                      disabled={isSubmitting}
                    />
                    {errors.label && (
                      <p className="mt-1 text-sm text-red-600">{errors.label.message}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="href" className="block text-sm font-medium text-gray-700 mb-1">
                      URL Path *
                    </label>
                    <input
                      id="href"
                      type="text"
                      {...register('href', { required: 'URL path is required' })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                      disabled={isSubmitting}
                    />
                    {errors.href && (
                      <p className="mt-1 text-sm text-red-600">{errors.href.message}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="parent_category_id" className="block text-sm font-medium text-gray-700 mb-1">
                      Parent Category
                    </label>
                    <select
                      id="parent_category_id"
                      {...register('parent_category_id')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                      disabled={isSubmitting}
                    >
                      <option value="">None (Top-level category)</option>
                      {flatCategories.map((cat) => (
                        <option key={cat.id} value={cat.id}>
                          {cat.depth > 0 ? `${Array(cat.depth).fill('—').join('')} ` : ''}{cat.label}
                          {cat.depth > 0 ? ` — ${cat.fullPath}` : ''}
                        </option>
                      ))}
                    </select>
                    <p className="mt-1 text-xs text-gray-500">
                      Changing the parent will affect the category hierarchy
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-1">
                        Icon (optional)
                      </label>
                      <input
                        id="icon"
                        type="text"
                        {...register('icon')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                        disabled={isSubmitting}
                      />
                    </div>

                    <div>
                      <label htmlFor="library" className="block text-sm font-medium text-gray-700 mb-1">
                        Icon Library (optional)
                      </label>
                      <input
                        id="library"
                        type="text"
                        {...register('library')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        id="isActive"
                        type="checkbox"
                        {...register('isActive')}
                        className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        disabled={isSubmitting}
                      />
                      <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                        Active
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        id="requirePartNumber"
                        type="checkbox"
                        {...register('requirePartNumber')}
                        className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        disabled={isSubmitting}
                      />
                      <label htmlFor="requirePartNumber" className="ml-2 block text-sm text-gray-700">
                        Require Part Number
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        id="isEnginePart"
                        type="checkbox"
                        {...register('isEnginePart')}
                        className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        disabled={isSubmitting}
                      />
                      <label htmlFor="isEnginePart" className="ml-2 block text-sm text-gray-700">
                        Engine Part
                      </label>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 mr-2"
                      disabled={isSubmitting}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4 mr-2" />
                          Save Changes
                        </>
                      )}
                    </button>
                  </div>
                </form>
              )}

              {/* Attributes Tab */}
              {activeTab === 'attributes' && (
                <div className="h-[500px] overflow-y-auto">
                  <AttributesTab
                    categoryId={category.id}
                    onRefresh={(updatedCategory) => {
                      // Pass the updated category data to the parent component
                      onSuccess(updatedCategory);
                    }}
                  />
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EditCategoryModal;
