import { apiClient } from './api';
import { CONFIG } from '../constants/config';
import {
  Part,
  Category,
  PaginatedResponse,
  SearchFilters,
  ApiResponse
} from '../types';

export class PartsService {
  // Get parts list with pagination and filters
  async getParts(
    page: number = 1,
    limit: number = CONFIG.SETTINGS.DEFAULT_PAGE_SIZE,
    filters?: SearchFilters
  ): Promise<PaginatedResponse<Part>> {
    try {
      const params: Record<string, any> = {
        page,
        limit,
      };

      if (filters) {
        if (filters.sort) params.sort = filters.sort;
        if (filters.category) params.filter = `category:${filters.category}`;
        if (filters.query) params.query = filters.query;
      }

      const response = await apiClient.get<PaginatedResponse<Part>>(
        CONFIG.ENDPOINTS.PARTS.LIST,
        params
      );

      return response;
    } catch (error) {
      console.error('Get parts error:', error);
      throw error;
    }
  }

  // Search parts
  async searchParts(
    query: string,
    page: number = 1,
    limit: number = CONFIG.SETTINGS.DEFAULT_PAGE_SIZE,
    filters?: Omit<SearchFilters, 'query'>
  ): Promise<PaginatedResponse<Part>> {
    try {
      const params: Record<string, any> = {
        query,
        page,
        limit,
      };

      if (filters) {
        if (filters.sort) params.sort = filters.sort;
        if (filters.category) params.filter = `category:${filters.category}`;
        if (filters.minPrice) params.minPrice = filters.minPrice;
        if (filters.maxPrice) params.maxPrice = filters.maxPrice;
        if (filters.condition) params.condition = filters.condition;
      }

      const response = await apiClient.get<PaginatedResponse<Part>>(
        CONFIG.ENDPOINTS.PARTS.SEARCH,
        params
      );

      return response;
    } catch (error) {
      console.error('Search parts error:', error);
      throw error;
    }
  }

  // Get part details
  async getPartDetails(partId: number): Promise<Part> {
    try {
      const response = await apiClient.get<Part>(
        CONFIG.ENDPOINTS.PARTS.DETAILS(partId)
      );

      return response;
    } catch (error) {
      console.error('Get part details error:', error);
      throw error;
    }
  }

  // Get parts by category
  async getPartsByCategory(
    categoryId: number,
    page: number = 1,
    limit: number = CONFIG.SETTINGS.DEFAULT_PAGE_SIZE,
    sort?: SearchFilters['sort']
  ): Promise<PaginatedResponse<Part>> {
    try {
      const params: Record<string, any> = {
        page,
        limit,
        filter: `category:${categoryId}`,
      };

      if (sort) params.sort = sort;

      const response = await apiClient.get<PaginatedResponse<Part>>(
        CONFIG.ENDPOINTS.PARTS.LIST,
        params
      );

      return response;
    } catch (error) {
      console.error('Get parts by category error:', error);
      throw error;
    }
  }

  // Filter parts by car
  async filterPartsByCar(
    brandId?: number,
    modelId?: number,
    generationId?: number,
    variationId?: number,
    trimId?: number,
    page: number = 1,
    limit: number = CONFIG.SETTINGS.DEFAULT_PAGE_SIZE
  ): Promise<PaginatedResponse<Part>> {
    try {
      const params: Record<string, any> = {
        page,
        limit,
      };

      if (brandId) params.brand = brandId;
      if (modelId) params.model = modelId;
      if (generationId) params.generation = generationId;
      if (variationId) params.variation = variationId;
      if (trimId) params.trim = trimId;

      const response = await apiClient.get<PaginatedResponse<Part>>(
        CONFIG.ENDPOINTS.PARTS.FILTER_BY_CAR,
        params
      );

      return response;
    } catch (error) {
      console.error('Filter parts by car error:', error);
      throw error;
    }
  }

  // Get categories
  async getCategories(): Promise<Category[]> {
    try {
      const response = await apiClient.get<Category[]>(
        CONFIG.ENDPOINTS.PARTS.CATEGORIES
      );

      return response;
    } catch (error) {
      console.error('Get categories error:', error);
      throw error;
    }
  }

  // Add new part (admin only)
  async addPart(partData: Partial<Part>): Promise<ApiResponse<Part>> {
    try {
      const response = await apiClient.post<ApiResponse<Part>>(
        CONFIG.ENDPOINTS.PARTS.ADD,
        partData
      );

      return response;
    } catch (error) {
      console.error('Add part error:', error);
      throw error;
    }
  }

  // Update part (admin only)
  async updatePart(partId: number, partData: Partial<Part>): Promise<ApiResponse<Part>> {
    try {
      const response = await apiClient.put<ApiResponse<Part>>(
        CONFIG.ENDPOINTS.PARTS.UPDATE(partId),
        partData
      );

      return response;
    } catch (error) {
      console.error('Update part error:', error);
      throw error;
    }
  }

  // Delete part (admin only)
  async deletePart(partId: number): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.delete<ApiResponse<void>>(
        CONFIG.ENDPOINTS.PARTS.DELETE(partId)
      );

      return response;
    } catch (error) {
      console.error('Delete part error:', error);
      throw error;
    }
  }

  // Upload part image
  async uploadPartImage(partId: number, imageFile: any): Promise<ApiResponse<string>> {
    try {
      const response = await apiClient.uploadFile<ApiResponse<string>>(
        `/api/parts/${partId}/upload-image`,
        imageFile,
        { partId }
      );

      return response;
    } catch (error) {
      console.error('Upload part image error:', error);
      throw error;
    }
  }
}

export const partsService = new PartsService();
export default partsService;
