import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  StatusBar
} from 'react-native';
import { COLORS, SPACING } from '../constants/theme';
import PinInput from '../components/PinInput';
import { pinAuthService } from '../services/PinAuthService';

interface PinSetupScreenProps {
  userId: string;
  onSetupComplete: () => void;
  onCancel?: () => void;
}

type SetupStep = 'enter' | 'confirm';

const PinSetupScreen: React.FC<PinSetupScreenProps> = ({
  userId,
  onSetupComplete,
  onCancel
}) => {
  const [step, setStep] = useState<SetupStep>('enter');
  const [firstPin, setFirstPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleFirstPinComplete = (pin: string) => {
    setFirstPin(pin);
    setStep('confirm');
    setError('');
  };

  const handleConfirmPinComplete = async (pin: string) => {
    if (pin !== firstPin) {
      setError('PINs do not match. Please try again.');
      setStep('enter');
      setFirstPin('');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await pinAuthService.setupPin(userId, pin);
      
      if (result.success) {
        Alert.alert(
          'PIN Setup Complete!',
          'Your PIN has been set up successfully. You can now use it for quick login.',
          [
            {
              text: 'OK',
              onPress: onSetupComplete
            }
          ]
        );
      } else {
        setError(result.error || 'Failed to set up PIN');
        setStep('enter');
        setFirstPin('');
      }
    } catch (error) {
      console.error('PIN setup error:', error);
      setError('An error occurred. Please try again.');
      setStep('enter');
      setFirstPin('');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (step === 'confirm') {
      // Go back to first step
      setStep('enter');
      setFirstPin('');
      setError('');
    } else {
      // Cancel entire setup
      if (onCancel) {
        onCancel();
      }
    }
  };

  const getTitle = () => {
    switch (step) {
      case 'enter':
        return 'Set up your PIN';
      case 'confirm':
        return 'Confirm your PIN';
      default:
        return 'Set up PIN';
    }
  };

  const getSubtitle = () => {
    switch (step) {
      case 'enter':
        return 'Choose a 4-digit PIN for quick and secure access to your account';
      case 'confirm':
        return 'Please enter your PIN again to confirm';
      default:
        return '';
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.BACKGROUND} />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>🔐 Secure Access</Text>
        <Text style={styles.headerSubtitle}>
          Set up a PIN for quick access to your AutoFlow account
        </Text>
      </View>

      <View style={styles.content}>
        <PinInput
          title={getTitle()}
          subtitle={getSubtitle()}
          onPinComplete={step === 'enter' ? handleFirstPinComplete : handleConfirmPinComplete}
          onCancel={handleCancel}
          maxLength={4}
          isLoading={isLoading}
          error={error}
          showCancel={true}
        />
      </View>

      <View style={styles.footer}>
        <View style={styles.securityInfo}>
          <Text style={styles.securityTitle}>🛡️ Security Features</Text>
          <Text style={styles.securityText}>• PIN is stored securely on your device</Text>
          <Text style={styles.securityText}>• 5 failed attempts will lock you out for 5 minutes</Text>
          <Text style={styles.securityText}>• You can change your PIN anytime in settings</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.lg,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
  footer: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  securityInfo: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.lg,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.PRIMARY,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.sm,
  },
  securityText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
    marginBottom: 4,
  },
});

export default PinSetupScreen;
