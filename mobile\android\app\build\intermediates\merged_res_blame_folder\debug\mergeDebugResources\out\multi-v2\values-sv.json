{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,6535", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,6610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "7238", "endColumns": "100", "endOffsets": "7334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,294,365,433,514,581,648,722,799,881,960,1029,1111,1194,1271,1354,1433,1510,1580,1649,1734,1814,1889", "endColumns": "72,82,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "123,206,289,360,428,509,576,643,717,794,876,955,1024,1106,1189,1266,1349,1428,1505,1575,1644,1729,1809,1884,1962"}, "to": {"startLines": "29,32,33,52,53,54,55,56,57,58,59,60,61,63,65,66,67,68,69,70,71,72,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2778,3135,3218,5600,5671,5739,5820,5887,5954,6028,6105,6187,6266,6453,6615,6698,6775,6858,6937,7014,7084,7153,7339,7419,7494", "endColumns": "72,82,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "2846,3213,3296,5666,5734,5815,5882,5949,6023,6100,6182,6261,6330,6530,6693,6770,6853,6932,7009,7079,7148,7233,7414,7489,7567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,339,448,602,688,833,946,1063,1184,1321,1451,1573,1702,1838,1943,2101,2227,2369,2511,2638,2756,2850,2983,3076,3201,3307,3445", "endColumns": "157,125,108,153,85,144,112,116,120,136,129,121,128,135,104,157,125,141,141,126,117,93,132,92,124,105,137,97", "endOffsets": "208,334,443,597,683,828,941,1058,1179,1316,1446,1568,1697,1833,1938,2096,2222,2364,2506,2633,2751,2845,2978,3071,3196,3302,3440,3538"}, "to": {"startLines": "30,31,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,62,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2851,3009,3301,3410,3564,3650,3795,3908,4025,4146,4283,4413,4535,4664,4800,4905,5063,5189,5331,5473,6335,7572,7666,7799,7892,8017,8123,8261", "endColumns": "157,125,108,153,85,144,112,116,120,136,129,121,128,135,104,157,125,141,141,126,117,93,132,92,124,105,137,97", "endOffsets": "3004,3130,3405,3559,3645,3790,3903,4020,4141,4278,4408,4530,4659,4795,4900,5058,5184,5326,5468,5595,6448,7661,7794,7887,8012,8118,8256,8354"}}]}]}