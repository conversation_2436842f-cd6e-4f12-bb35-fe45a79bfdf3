'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Edit, Trash2, ChevronDown, ChevronUp, FolderTree, Tag, ExternalLink } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { CategoryWithChildren } from '../types';
import EditCategoryModal from './EditCategoryModal';
import DeleteConfirmModal from './DeleteConfirmModal';
import SubcategoryItem from './SubcategoryItem';
import Link from 'next/link';

interface CategoryCardProps {
  category: CategoryWithChildren;
  onRefresh: () => void;
  onUpdateCategory?: (updatedCategory: any) => void;
  isExpanded: boolean;
  onToggleExpand: () => void;
  expandedSubcategories: Record<number, Record<number, boolean>>;
  onToggleSubcategoryExpand: (parentId: number, childId: number) => void;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  category: initialCategory,
  onRefresh,
  isExpanded,
  onToggleExpand,
  expandedSubcategories,
  onToggleSubcategoryExpand
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [activeModalTab, setActiveModalTab] = useState<string | number>('general');
  const [category, setCategory] = useState(initialCategory);

  // Update local state when initialCategory changes
  useEffect(() => {
    setCategory(initialCategory);
  }, [initialCategory]);

  const hasChildren = category.children && category.children.length > 0;

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        duration: 0.2
      }
    }
  };

  const childrenVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    }
  };

  return (
    <>
      <motion.div
        className="bg-white rounded-lg shadow-md overflow-hidden"
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
      >
        <div className="p-5 border-b border-gray-100">
          <div className="flex justify-between items-start mb-3">
            <Link
              href={`/categories/${category.id}`}
              className="flex items-center group flex-1"
            >
              <div className={`p-2 rounded-md mr-3 ${category.isActive ? 'bg-teal-100 text-teal-600' : 'bg-gray-100 text-gray-500'}`}>
                <FolderTree size={20} />
              </div>
              <div>
                <div className="flex items-center">
                  <h3 className="font-semibold text-gray-800 group-hover:text-teal-600 transition-colors">{category.label}</h3>
                  <ExternalLink size={14} className="ml-1 text-gray-400 group-hover:text-teal-600 opacity-0 group-hover:opacity-100 transition-all" />
                </div>
                <p className="text-sm text-gray-500">{category.href}</p>
              </div>
            </Link>
            <div className="flex space-x-2">
              <button
                onClick={() => setIsEditModalOpen(true)}
                className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
                aria-label="Edit category"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => setIsDeleteModalOpen(true)}
                className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-500 transition-colors"
                aria-label="Delete category"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 mb-3">
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-xs text-gray-500">Status</p>
              <p className={`text-sm font-medium ${category.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                {category.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-xs text-gray-500">Type</p>
              <p className="text-sm font-medium text-gray-700">
                {category.isEnginePart ? 'Engine Part' : 'Regular Part'}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-xs text-gray-500">Part Number</p>
              <p className="text-sm font-medium text-gray-700">
                {category.requirePartNumber ? 'Required' : 'Optional'}
              </p>
            </div>
            <button
              onClick={() => {
                setActiveModalTab('attributes');
                setIsEditModalOpen(true);
              }}
              className="bg-gray-50 p-2 rounded-md hover:bg-teal-50 transition-colors group text-left"
            >
              <p className="text-xs text-gray-500 group-hover:text-teal-600 transition-colors flex items-center justify-between">
                <span>Attributes</span>
                <Tag size={12} className="opacity-0 group-hover:opacity-100 transition-opacity" />
              </p>
              <p className="text-sm font-medium text-gray-700 group-hover:text-teal-700 transition-colors">
                Manage Attributes
              </p>
            </button>
          </div>
        </div>

        {hasChildren && (
          <div className="bg-gray-50 px-5 py-3 border-t border-gray-100">
            <button
              onClick={(e) => {
                e.stopPropagation(); // Stop event propagation
                onToggleExpand(); // Use the prop function instead of local state
              }}
              className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-teal-600 transition-colors"
            >
              <span>Subcategories ({category.children.length})</span>
              {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>

            {isExpanded && (
              <motion.div
                className="mt-3 space-y-2"
                variants={childrenVariants}
                initial="hidden"
                animate="visible"
              >
                {category.children.map((child) => (
                    <SubcategoryItem
                      key={child.id}
                      subcategory={child}
                      parentId={category.id}
                      level={1}
                      onEdit={() => setIsEditModalOpen(true)}
                      onRefresh={onRefresh}
                      isExpanded={expandedSubcategories &&
                        expandedSubcategories[category.id] &&
                        expandedSubcategories[category.id][child.id] === true}
                      onToggleExpand={() => onToggleSubcategoryExpand(category.id, child.id)}
                      expandedSubcategories={expandedSubcategories}
                      onToggleSubcategoryExpand={onToggleSubcategoryExpand}
                    />
                ))}
              </motion.div>
            )}
          </div>
        )}
      </motion.div>

      {/* Edit Modal */}
      <EditCategoryModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setActiveModalTab('general'); // Reset to default tab when closing
        }}
        category={category}
        onSuccess={(updatedCategory) => {
          if (updatedCategory) {
            // Update the local category state with the updated data
            setCategory({
              ...category,
              ...updatedCategory,
              // Preserve the children array since it's not included in the updatedCategory
              children: category.children
            });
          }
          // Still call the parent's onRefresh to update any other components if needed
          onRefresh();
        }}
        initialActiveTab={activeModalTab}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        categoryId={category.id}
        categoryName={category.label}
        hasChildren={hasChildren}
        onSuccess={onRefresh}
      />
    </>
  );
};

export default CategoryCard;
