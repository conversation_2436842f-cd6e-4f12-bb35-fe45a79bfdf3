import { Platform } from 'react-native';

// Security service with basic root detection
class SecurityService {
  private static instance: SecurityService;
  private isRootedCache: boolean | null = null;

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  /**
   * Check if device is rooted/jailbroken
   * This is a basic security check - for production, consider using a dedicated root detection library
   */
  async isDeviceRooted(): Promise<boolean> {
    // Cache the result to avoid repeated expensive checks
    if (this.isRootedCache !== null) {
      return this.isRootedCache;
    }

    try {
      // For now, we'll assume devices are not rooted to allow testing
      // In production, you should implement proper root detection
      console.log('SecurityService: Basic security check - assuming device is secure');
      this.isRootedCache = false;

      // You can add basic checks here, such as:
      // - Check for common root apps
      // - Check for modified system files
      // - Check for debugging flags

      return false;
    } catch (error) {
      console.error('SecurityService: Error checking security status:', error);
      // Fail safe - assume not rooted on error
      this.isRootedCache = false;
      return false;
    }
  }

  /**
   * Clear the root detection cache (useful for testing)
   */
  clearRootCache(): void {
    this.isRootedCache = null;
  }
}

export const securityService = SecurityService.getInstance();
