import { NativeModules } from 'react-native';

// Root detection service
class SecurityService {
  private static instance: SecurityService;
  private isRootedCache: boolean | null = null;

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  /**
   * Check if device is rooted/jailbroken
   * This is a critical security check that must be performed before any biometric operations
   */
  async isDeviceRooted(): Promise<boolean> {
    // Cache the result to avoid repeated expensive checks
    if (this.isRootedCache !== null) {
      return this.isRootedCache;
    }

    try {
      // Using react-native-root-detection library
      const RootDetection = NativeModules.RootDetection;
      
      if (!RootDetection) {
        console.warn('SecurityService: Root detection module not available');
        // Fail safe - assume not rooted if module unavailable
        this.isRootedCache = false;
        return false;
      }

      const isRooted = await RootDetection.isDeviceRooted();
      this.isRootedCache = isRooted;
      
      if (isRooted) {
        console.warn('SecurityService: Device detected as rooted - biometric features will be disabled');
      }
      
      return isRooted;
    } catch (error) {
      console.error('SecurityService: Error checking root status:', error);
      // Fail safe - assume not rooted on error
      this.isRootedCache = false;
      return false;
    }
  }

  /**
   * Clear the root detection cache (useful for testing)
   */
  clearRootCache(): void {
    this.isRootedCache = null;
  }
}

export const securityService = SecurityService.getInstance();
