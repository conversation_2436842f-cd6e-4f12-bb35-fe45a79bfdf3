{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,283,381,504,585,704,801,900,999,1102,1204,1302,1404,1506,1596,1722,1824,1932,2035,2131,2235,2320,2425,2503,2601,2686,2791", "endColumns": "123,103,97,122,80,118,96,98,98,102,101,97,101,101,89,125,101,107,102,95,103,84,104,77,97,84,104,86", "endOffsets": "174,278,376,499,580,699,796,895,994,1097,1199,1297,1399,1501,1591,1717,1819,1927,2030,2126,2230,2315,2420,2498,2596,2681,2786,2873"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2732,2856,3211,3309,3432,3513,3632,3729,3828,3927,4030,4132,4230,4332,4434,4524,4650,4752,4860,4963,5752,6969,7054,7159,7237,7335,7420,7525", "endColumns": "123,103,97,122,80,118,96,98,98,102,101,97,101,101,89,125,101,107,102,95,103,84,104,77,97,84,104,86", "endOffsets": "2851,2955,3304,3427,3508,3627,3724,3823,3922,4025,4127,4225,4327,4429,4519,4645,4747,4855,4958,5054,5851,7049,7154,7232,7330,7415,7520,7607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,217,300,372,439,505,580,645,710,779,850,923,998,1065,1135,1208,1280,1357,1433,1505,1575,1644,1724,1792,1862,1929", "endColumns": "65,95,82,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,212,295,367,434,500,575,640,705,774,845,918,993,1060,1130,1203,1275,1352,1428,1500,1570,1639,1719,1787,1857,1924,1993"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2666,2960,3056,3139,5059,5126,5192,5267,5332,5397,5466,5537,5610,5685,5856,6005,6078,6150,6227,6303,6375,6445,6514,6695,6763,6833,6900", "endColumns": "65,95,82,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2727,3051,3134,3206,5121,5187,5262,5327,5392,5461,5532,5605,5680,5747,5921,6073,6145,6222,6298,6370,6440,6509,6589,6758,6828,6895,6964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,5926", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,6000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6594", "endColumns": "100", "endOffsets": "6690"}}]}]}