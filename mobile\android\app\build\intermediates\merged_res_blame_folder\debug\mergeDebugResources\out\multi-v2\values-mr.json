{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,235,331,410,479,550,632,699,766,840,916,996,1076,1144,1227,1309,1384,1470,1557,1632,1703,1774,1865,1937,2012,2081", "endColumns": "68,110,95,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "119,230,326,405,474,545,627,694,761,835,911,991,1071,1139,1222,1304,1379,1465,1552,1627,1698,1769,1860,1932,2007,2076,2149"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2795,3155,3266,3362,5753,5822,5893,5975,6042,6109,6183,6259,6339,6419,6621,6784,6866,6941,7027,7114,7189,7260,7331,7523,7595,7670,7739", "endColumns": "68,110,95,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "2859,3261,3357,3436,5817,5888,5970,6037,6104,6178,6254,6334,6414,6482,6699,6861,6936,7022,7109,7184,7255,7326,7417,7590,7665,7734,7807"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,6704", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,6779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7422", "endColumns": "100", "endOffsets": "7518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,346,453,602,689,840,958,1071,1193,1322,1451,1585,1717,1851,1947,2111,2243,2387,2532,2658,2792,2888,3017,3102,3220,3318,3449", "endColumns": "161,128,106,148,86,150,117,112,121,128,128,133,131,133,95,163,131,143,144,125,133,95,128,84,117,97,130,97", "endOffsets": "212,341,448,597,684,835,953,1066,1188,1317,1446,1580,1712,1846,1942,2106,2238,2382,2527,2653,2787,2883,3012,3097,3215,3313,3444,3542"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2864,3026,3441,3548,3697,3784,3935,4053,4166,4288,4417,4546,4680,4812,4946,5042,5206,5338,5482,5627,6487,7812,7908,8037,8122,8240,8338,8469", "endColumns": "161,128,106,148,86,150,117,112,121,128,128,133,131,133,95,163,131,143,144,125,133,95,128,84,117,97,130,97", "endOffsets": "3021,3150,3543,3692,3779,3930,4048,4161,4283,4412,4541,4675,4807,4941,5037,5201,5333,5477,5622,5748,6616,7903,8032,8117,8235,8333,8464,8562"}}]}]}