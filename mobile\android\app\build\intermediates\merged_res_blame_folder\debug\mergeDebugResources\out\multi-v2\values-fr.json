{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,341,459,637,729,903,1031,1153,1276,1425,1585,1727,1858,2029,2127,2307,2432,2575,2736,2867,3004,3104,3250,3358,3512,3626,3786", "endColumns": "165,119,117,177,91,173,127,121,122,148,159,141,130,170,97,179,124,142,160,130,136,99,145,107,153,113,159,116", "endOffsets": "216,336,454,632,724,898,1026,1148,1271,1420,1580,1722,1853,2024,2122,2302,2427,2570,2731,2862,2999,3099,3245,3353,3507,3621,3781,3898"}, "to": {"startLines": "30,31,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,60,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2917,3083,3374,3492,3670,3762,3936,4064,4186,4309,4458,4618,4760,4891,5062,5160,5340,5465,5608,5769,6534,7896,7996,8142,8250,8404,8518,8678", "endColumns": "165,119,117,177,91,173,127,121,122,148,159,141,130,170,97,179,124,142,160,130,136,99,145,107,153,113,159,116", "endOffsets": "3078,3198,3487,3665,3757,3931,4059,4181,4304,4453,4613,4755,4886,5057,5155,5335,5460,5603,5764,5895,6666,7991,8137,8245,8399,8513,8673,8790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,213,296,366,449,516,595,677,767,859,930,1017,1092,1179,1259,1339,1414,1491,1564,1655,1734,1815,1887", "endColumns": "69,87,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "120,208,291,361,444,511,590,672,762,854,925,1012,1087,1174,1254,1334,1409,1486,1559,1650,1729,1810,1882,1962"}, "to": {"startLines": "29,32,33,52,53,54,55,56,57,58,59,61,63,64,65,66,67,68,69,70,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2847,3203,3291,5900,5970,6053,6120,6199,6281,6371,6463,6671,6845,6920,7007,7087,7167,7242,7319,7392,7584,7663,7744,7816", "endColumns": "69,87,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "2912,3286,3369,5965,6048,6115,6194,6276,6366,6458,6529,6753,6915,7002,7082,7162,7237,7314,7387,7478,7658,7739,7811,7891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,6758", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,6840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "71", "startColumns": "4", "startOffsets": "7483", "endColumns": "100", "endOffsets": "7579"}}]}]}