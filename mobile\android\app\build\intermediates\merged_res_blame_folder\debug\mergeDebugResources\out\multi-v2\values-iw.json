{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7229", "endColumns": "100", "endOffsets": "7325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,6530", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,6607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,221,308,386,456,525,606,674,742,820,898,980,1059,1130,1208,1288,1361,1441,1519,1594,1666,1738,1825,1896,1975,2044", "endColumns": "68,96,86,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,216,303,381,451,520,601,669,737,815,893,975,1054,1125,1203,1283,1356,1436,1514,1589,1661,1733,1820,1891,1970,2039,2114"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2761,3101,3198,3285,5588,5658,5727,5808,5876,5944,6022,6100,6182,6261,6452,6612,6692,6765,6845,6923,6998,7070,7142,7330,7401,7480,7549", "endColumns": "68,96,86,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "2825,3193,3280,3358,5653,5722,5803,5871,5939,6017,6095,6177,6256,6327,6525,6687,6760,6840,6918,6993,7065,7137,7224,7396,7475,7544,7619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,326,432,584,675,819,933,1042,1162,1291,1415,1546,1660,1787,1881,2032,2153,2296,2438,2551,2671,2769,2896,2987,3107,3205,3332", "endColumns": "150,119,105,151,90,143,113,108,119,128,123,130,113,126,93,150,120,142,141,112,119,97,126,90,119,97,126,96", "endOffsets": "201,321,427,579,670,814,928,1037,1157,1286,1410,1541,1655,1782,1876,2027,2148,2291,2433,2546,2666,2764,2891,2982,3102,3200,3327,3424"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2830,2981,3363,3469,3621,3712,3856,3970,4079,4199,4328,4452,4583,4697,4824,4918,5069,5190,5333,5475,6332,7624,7722,7849,7940,8060,8158,8285", "endColumns": "150,119,105,151,90,143,113,108,119,128,123,130,113,126,93,150,120,142,141,112,119,97,126,90,119,97,126,96", "endOffsets": "2976,3096,3464,3616,3707,3851,3965,4074,4194,4323,4447,4578,4692,4819,4913,5064,5185,5328,5470,5583,6447,7717,7844,7935,8055,8153,8280,8377"}}]}]}