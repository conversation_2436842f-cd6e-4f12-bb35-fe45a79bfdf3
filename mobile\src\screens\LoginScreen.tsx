import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Linking
} from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY } from '../constants/theme';
import { biometricAuthService } from '../services/BiometricAuthService';
import { BiometricErrorHandler } from '../components/BiometricErrorHandler';
import { BiometricErrorCodes } from '../types/biometric.types';
import { simpleStorage } from '../services/storage';

type Screen = 'Home' | 'Login' | 'Parts' | 'Profile';

interface Props {
  onNavigate: (screen: Screen) => void;
  onNavigateToSettings?: () => void;
}

const LoginScreen: React.FC<Props> = ({ onNavigate, onNavigateToSettings }) => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [isLoading, setIsLoading] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricKeysExist, setBiometricKeysExist] = useState(false);
  const [biometricError, setBiometricError] = useState<string>('');
  const [biometryType, setBiometryType] = useState<string>('');

  useEffect(() => {
    console.log('LoginScreen: Component mounted, checking biometric availability...');
    checkBiometricAvailability();

    // Debug: Show alert to confirm our code is running
    setTimeout(() => {
      Alert.alert('Debug', 'LoginScreen loaded. Biometric check should have run.');
    }, 1000);
  }, []);

  const checkBiometricAvailability = async () => {
    try {
      console.log('LoginScreen: Checking biometric availability...');
      const supportInfo = await biometricAuthService.checkBiometricSupport();
      console.log('LoginScreen: Support info:', supportInfo);

      setBiometricAvailable(supportInfo.isSupported);
      setBiometryType(supportInfo.biometryType || '');

      if (supportInfo.isSupported) {
        const keysExist = await biometricAuthService.doesBiometricKeyExist();
        console.log('LoginScreen: Keys exist:', keysExist);
        setBiometricKeysExist(keysExist);
      } else {
        console.log('LoginScreen: Biometric not supported:', supportInfo.error);
      }
    } catch (error) {
      console.error('LoginScreen: Error checking biometric availability:', error);
    }
  };

  const handleTraditionalLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call - replace with your actual login logic
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Store user credentials for demo
      await simpleStorage.setItem('user_email', email);
      await simpleStorage.setItem('user_logged_in', 'true');

      // Ask about biometric setup if available and not already enrolled
      console.log('LoginScreen: After login - biometricAvailable:', biometricAvailable, 'biometricKeysExist:', biometricKeysExist);

      // Offer quick login setup options
      console.log('LoginScreen: Offering quick login setup options');
      Alert.alert(
        'Enable Quick Login?',
        'Choose a method for faster login next time:',
        [
          {
            text: 'Not Now',
            style: 'cancel',
            onPress: () => onNavigate('Home'),
          },
          {
            text: 'Setup PIN',
            onPress: () => {
              if (onNavigateToSettings) {
                onNavigateToSettings();
              } else {
                onNavigate('Home');
              }
            },
          },
          ...(biometricAvailable ? [{
            text: `Enable ${getBiometricLabel()}`,
            onPress: () => enableBiometric(),
          }] : []),
        ]
      );

      // Original logic (commented out for testing)
      /*
      if (biometricAvailable && !biometricKeysExist) {
        console.log('LoginScreen: Showing biometric setup prompt');
        Alert.alert(
          'Enable Biometric Login?',
          `Would you like to enable ${getBiometricLabel()} for faster login next time?`,
          [
            {
              text: 'Not Now',
              style: 'cancel',
              onPress: () => onNavigate('Home'),
            },
            {
              text: 'Enable',
              onPress: () => enableBiometric(),
            },
          ]
        );
      } else {
        console.log('LoginScreen: Not showing biometric prompt - going to Home');
        onNavigate('Home');
      }
      */
    } catch (error) {
      Alert.alert('Login Failed', 'Invalid email or password');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBiometricLogin = async () => {
    if (!email.trim()) {
      Alert.alert('Email Required', 'Please enter your email address first');
      return;
    }

    setIsLoading(true);
    setBiometricError('');

    try {
      const result = await biometricAuthService.authenticate(email);

      if (result.success && result.token) {
        // Store user credentials
        await simpleStorage.setItem('user_email', email);
        await simpleStorage.setItem('user_logged_in', 'true');
        onNavigate('Home');
      } else {
        setBiometricError(result.errorCode || 'UNKNOWN_ERROR');
      }
    } catch (error) {
      console.error('LoginScreen: Biometric login error:', error);
      setBiometricError(BiometricErrorCodes.ERROR_UNABLE_TO_PROCESS);
    } finally {
      setIsLoading(false);
    }
  };

  const enableBiometric = async () => {
    setIsLoading(true);
    try {
      const result = await biometricAuthService.enrollBiometrics(email);

      if (result.success) {
        setBiometricKeysExist(true);
        Alert.alert(
          'Success!',
          `${getBiometricLabel()} has been enabled! You can now use it to login quickly.`,
          [{ text: 'OK', onPress: () => onNavigate('Home') }]
        );
      } else {
        Alert.alert(
          'Setup Failed',
          result.error || 'Could not enable biometric authentication. You can try again later in settings.',
          [{ text: 'OK', onPress: () => onNavigate('Home') }]
        );
      }
    } catch (error) {
      console.error('Error enabling biometric:', error);
      Alert.alert(
        'Setup Failed',
        'Could not enable biometric authentication. You can try again later in settings.',
        [{ text: 'OK', onPress: () => onNavigate('Home') }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleBiometricRetry = () => {
    setBiometricError('');
    handleBiometricLogin();
  };

  const handleFallbackToPassword = () => {
    setBiometricError('');
    Alert.alert('Use Password', 'Please enter your password to continue');
  };

  const handleOpenSettings = () => {
    setBiometricError('');
    Alert.alert(
      'Open Settings',
      'Please go to your device settings to set up biometric authentication',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => Linking.openSettings() }
      ]
    );
  };

  const handleReEnrollBiometric = () => {
    setBiometricError('');
    if (onNavigateToSettings) {
      Alert.alert(
        'Re-enroll Required',
        'Your biometric data has changed. Please go to settings to re-enroll.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Go to Settings', onPress: onNavigateToSettings }
        ]
      );
    }
  };

  const getBiometricButtonText = () => {
    if (biometryType.toLowerCase().includes('face')) {
      return 'Login with Face ID';
    } else if (biometryType.toLowerCase().includes('touch')) {
      return 'Login with Touch ID';
    } else {
      return 'Login with Fingerprint';
    }
  };

  const getBiometricIcon = () => {
    if (biometryType.toLowerCase().includes('face')) {
      return '👤';
    } else {
      return '👆';
    }
  };

  const getBiometricLabel = () => {
    if (biometryType.toLowerCase().includes('face')) {
      return 'Face ID';
    } else if (biometryType.toLowerCase().includes('touch')) {
      return 'Touch ID';
    } else {
      return 'Fingerprint';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>AutoFlow</Text>
        <Text style={styles.subtitle}>VW & Audi Parts Specialist</Text>
      </View>

      <View style={styles.form}>
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          autoCorrect={false}
        />

        <TextInput
          style={styles.input}
          placeholder="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          autoCapitalize="none"
          autoCorrect={false}
        />

        <TouchableOpacity
          style={[styles.loginButton, isLoading && styles.disabledButton]}
          onPress={handleTraditionalLogin}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.loginButtonText}>Login</Text>
          )}
        </TouchableOpacity>

        {/* Biometric Login Section */}
        {biometricAvailable && biometricKeysExist && (
          <>
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>OR</Text>
              <View style={styles.dividerLine} />
            </View>

            <TouchableOpacity
              style={[styles.biometricButton, isLoading && styles.disabledButton]}
              onPress={handleBiometricLogin}
              disabled={isLoading}
            >
              <Text style={styles.biometricIcon}>{getBiometricIcon()}</Text>
              <Text style={styles.biometricButtonText}>
                {getBiometricButtonText()}
              </Text>
            </TouchableOpacity>
          </>
        )}

        {/* Show setup message if biometric is available but not enrolled */}
        {biometricAvailable && !biometricKeysExist && (
          <View style={styles.biometricSetupContainer}>
            <Text style={styles.biometricSetupText}>
              Enable biometric login in settings for faster access
            </Text>
            {onNavigateToSettings && (
              <TouchableOpacity
                style={styles.setupButton}
                onPress={onNavigateToSettings}
              >
                <Text style={styles.setupButtonText}>Set Up Now</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      {/* Biometric Error Handler */}
      <BiometricErrorHandler
        errorCode={biometricError}
        onRetry={handleBiometricRetry}
        onFallback={handleFallbackToPassword}
        onSettings={handleOpenSettings}
        onReEnroll={handleReEnrollBiometric}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1a2332',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  form: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    marginBottom: 16,
    backgroundColor: '#f9f9f9',
  },
  loginButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#ddd',
  },
  dividerText: {
    marginHorizontal: 16,
    color: '#666',
    fontSize: 14,
  },
  biometricButton: {
    backgroundColor: '#34C759',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  biometricIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  biometricButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  biometricSetupContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  biometricSetupText: {
    color: '#007AFF',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 12,
  },
  setupButton: {
    backgroundColor: '#007AFF',
    borderRadius: 6,
    padding: 12,
    alignItems: 'center',
  },
  setupButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default LoginScreen;
