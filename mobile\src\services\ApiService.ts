import { 
  ChallengeResponse, 
  VerificationRequest, 
  VerificationResponse 
} from '../types/biometric.types';

class ApiService {
  private baseUrl: string;
  private authToken: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  setAuthToken(token: string): void {
    this.authToken = token;
  }

  private async makeRequest<T>(
    endpoint: string, 
    method: 'GET' | 'POST' | 'DELETE' = 'GET', 
    body?: any
  ): Promise<T> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Request a cryptographic challenge for biometric authentication
   */
  async getBiometricChallenge(userId: string): Promise<ChallengeResponse> {
    // For demo purposes, return a mock challenge
    // In production, this would call your actual backend
    console.log('ApiService: Getting biometric challenge for user:', userId);
    
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        const challenge = Buffer.from(`challenge_${Date.now()}_${Math.random()}`).toString('base64');
        resolve({
          challenge,
          expiresAt: Date.now() + (5 * 60 * 1000) // 5 minutes
        });
      }, 500);
    });
  }

  /**
   * Verify biometric signature with the backend
   */
  async verifyBiometricSignature(request: VerificationRequest): Promise<VerificationResponse> {
    console.log('ApiService: Verifying biometric signature for user:', request.userId);
    
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        // For demo purposes, always return success with a mock token
        // In production, this would verify the signature against the stored public key
        const token = `mock_jwt_token_${Date.now()}`;
        resolve({
          success: true,
          token
        });
      }, 1000);
    });
  }

  /**
   * Register a new biometric public key
   */
  async registerBiometricKey(userId: string, publicKey: string): Promise<{ success: boolean }> {
    console.log('ApiService: Registering biometric key for user:', userId);
    console.log('Public key length:', publicKey.length);
    
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true });
      }, 500);
    });
  }

  /**
   * Delete biometric public key from backend
   */
  async deleteBiometricKey(userId: string): Promise<{ success: boolean }> {
    console.log('ApiService: Deleting biometric key for user:', userId);
    
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true });
      }, 500);
    });
  }
}

// Export singleton instance
// Replace with your actual API base URL
export const apiService = new ApiService('https://api.autoflow.com');
