{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7548", "endColumns": "100", "endOffsets": "7644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,6832", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,6909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,237,333,419,493,567,655,727,794,870,949,1037,1123,1195,1276,1361,1437,1519,1602,1679,1752,1825,1910,1984,2064,2134", "endColumns": "73,107,95,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "124,232,328,414,488,562,650,722,789,865,944,1032,1118,1190,1271,1356,1432,1514,1597,1674,1747,1820,1905,1979,2059,2129,2214"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2829,3202,3310,3406,5836,5910,5984,6072,6144,6211,6287,6366,6454,6540,6751,6914,6999,7075,7157,7240,7317,7390,7463,7649,7723,7803,7873", "endColumns": "73,107,95,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "2898,3305,3401,3487,5905,5979,6067,6139,6206,6282,6361,6449,6535,6607,6827,6994,7070,7152,7235,7312,7385,7458,7543,7718,7798,7868,7953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,354,461,621,714,887,1009,1126,1253,1384,1525,1636,1763,1897,1996,2179,2311,2441,2573,2698,2837,2941,3094,3197,3349,3461,3622", "endColumns": "174,123,106,159,92,172,121,116,126,130,140,110,126,133,98,182,131,129,131,124,138,103,152,102,151,111,160,125", "endOffsets": "225,349,456,616,709,882,1004,1121,1248,1379,1520,1631,1758,1892,1991,2174,2306,2436,2568,2693,2832,2936,3089,3192,3344,3456,3617,3743"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2903,3078,3492,3599,3759,3852,4025,4147,4264,4391,4522,4663,4774,4901,5035,5134,5317,5449,5579,5711,6612,7958,8062,8215,8318,8470,8582,8743", "endColumns": "174,123,106,159,92,172,121,116,126,130,140,110,126,133,98,182,131,129,131,124,138,103,152,102,151,111,160,125", "endOffsets": "3073,3197,3594,3754,3847,4020,4142,4259,4386,4517,4658,4769,4896,5030,5129,5312,5444,5574,5706,5831,6746,8057,8210,8313,8465,8577,8738,8864"}}]}]}