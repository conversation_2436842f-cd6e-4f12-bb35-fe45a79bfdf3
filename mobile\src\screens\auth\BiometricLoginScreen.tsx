import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Image } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY } from '../../constants/theme';
import { authService } from '../../services/auth';
import { biometricService } from '../../services/biometric';

interface Props {
  onLoginSuccess: (user: any) => void;
  onFallbackToPassword: () => void;
  onCancel: () => void;
}

const BiometricLoginScreen: React.FC<Props> = ({
  onLoginSuccess,
  onFallbackToPassword,
  onCancel,
}) => {
  const [biometricType, setBiometricType] = useState<string | null>(null);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  useEffect(() => {
    initializeBiometric();
  }, []);

  const initializeBiometric = async () => {
    try {
      const type = await authService.getBiometricType();
      setBiometricType(type);
    } catch (error) {
      console.error('Error initializing biometric:', error);
    }
  };

  const handleBiometricLogin = async () => {
    try {
      setIsAuthenticating(true);
      
      const user = await authService.loginWithBiometric();
      onLoginSuccess(user);
    } catch (error: any) {
      console.error('Biometric login error:', error);
      
      if (error.message?.includes('cancelled') || error.message?.includes('canceled')) {
        // User cancelled, don't show error
        return;
      }
      
      Alert.alert(
        'Authentication Failed',
        'Biometric authentication failed. Would you like to try again or use your password?',
        [
          { text: 'Try Again', onPress: handleBiometricLogin },
          { text: 'Use Password', onPress: onFallbackToPassword },
          { text: 'Cancel', style: 'cancel', onPress: onCancel },
        ]
      );
    } finally {
      setIsAuthenticating(false);
    }
  };

  const getBiometricIcon = () => {
    switch (biometricType) {
      case 'FaceID':
        return '👤'; // Face icon
      case 'TouchID':
      case 'Fingerprint':
        return '👆'; // Fingerprint icon
      default:
        return '🔒'; // Lock icon
    }
  };

  const getBiometricTitle = () => {
    switch (biometricType) {
      case 'FaceID':
        return 'Face ID Login';
      case 'TouchID':
        return 'Touch ID Login';
      case 'Fingerprint':
        return 'Fingerprint Login';
      default:
        return 'Biometric Login';
    }
  };

  const getBiometricDescription = () => {
    switch (biometricType) {
      case 'FaceID':
        return 'Look at your device to authenticate';
      case 'TouchID':
        return 'Place your finger on the Touch ID sensor';
      case 'Fingerprint':
        return 'Place your finger on the fingerprint sensor';
      default:
        return 'Use your biometric authentication to login';
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.logo}>AutoFlow</Text>
        <Text style={styles.subtitle}>Secure Login</Text>
      </View>

      {/* Biometric Section */}
      <View style={styles.biometricSection}>
        <View style={styles.biometricIcon}>
          <Text style={styles.iconText}>{getBiometricIcon()}</Text>
        </View>
        
        <Text style={styles.biometricTitle}>{getBiometricTitle()}</Text>
        <Text style={styles.biometricDescription}>
          {getBiometricDescription()}
        </Text>

        {/* Authenticate Button */}
        <TouchableOpacity
          style={[styles.authenticateButton, isAuthenticating && styles.authenticatingButton]}
          onPress={handleBiometricLogin}
          disabled={isAuthenticating}
        >
          <Text style={styles.authenticateButtonText}>
            {isAuthenticating ? 'Authenticating...' : 'Authenticate'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Alternative Options */}
      <View style={styles.alternativeSection}>
        <TouchableOpacity style={styles.passwordButton} onPress={onFallbackToPassword}>
          <Text style={styles.passwordButtonText}>Use Password Instead</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
    paddingHorizontal: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    paddingTop: SPACING.xl * 2,
    paddingBottom: SPACING.xl,
  },
  logo: {
    fontSize: TYPOGRAPHY.sizes.xxxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.PRIMARY,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    color: COLORS.TEXT_SECONDARY,
  },
  biometricSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  biometricIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: COLORS.SURFACE,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.xl,
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
  },
  iconText: {
    fontSize: 48,
  },
  biometricTitle: {
    fontSize: TYPOGRAPHY.sizes.xxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  biometricDescription: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.xl * 2,
    lineHeight: 22,
  },
  authenticateButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.xl * 2,
    paddingVertical: SPACING.lg,
    borderRadius: 8,
    minWidth: 200,
    alignItems: 'center',
  },
  authenticatingButton: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  authenticateButtonText: {
    color: COLORS.WHITE,
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  alternativeSection: {
    paddingBottom: SPACING.xl,
    alignItems: 'center',
  },
  passwordButton: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  passwordButtonText: {
    color: COLORS.PRIMARY,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  cancelButton: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  cancelButtonText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: TYPOGRAPHY.sizes.md,
  },
});

export default BiometricLoginScreen;
