import { supabase } from './supabase';
import { apiClient } from './api';
import { CONFIG } from '../constants/config';
import { User, UserProfile, ApiResponse } from '../types';
import { biometricService, BiometricCredentials } from './biometric';
import AsyncStorage from '@react-native-async-storage/async-storage';

export class AuthService {
  // Sign in with email and password (Phase 1: Validate credentials)
  async signInWithPassword(email: string, password: string): Promise<boolean> {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Immediately sign out to prevent session creation (as per your flow)
      await supabase.auth.signOut();
      
      return true;
    } catch (error) {
      console.error('Password validation error:', error);
      throw error;
    }
  }

  // Send OTP (Phase 2: Initiate OTP flow)
  async sendOTP(email: string): Promise<void> {
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
        },
      });

      if (error) throw error;
    } catch (error) {
      console.error('OTP send error:', error);
      throw error;
    }
  }

  // Verify OTP and complete sign in
  async verifyOTP(email: string, token: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email',
      });

      if (error) throw error;
      if (!data.user) throw new Error('No user data received');

      return data.user as User;
    } catch (error) {
      console.error('OTP verification error:', error);
      throw error;
    }
  }

  // Complete login flow (password validation + OTP)
  async login(email: string, password: string): Promise<{ requiresOTP: boolean }> {
    try {
      // Phase 1: Validate credentials
      await this.signInWithPassword(email, password);
      
      // Phase 2: Send OTP
      await this.sendOTP(email);
      
      return { requiresOTP: true };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Complete OTP verification
  async completeLogin(email: string, otp: string, enableBiometric: boolean = false): Promise<User> {
    try {
      const user = await this.verifyOTP(email, otp);

      // If user wants to enable biometric
      if (enableBiometric && user.id) {
        try {
          await biometricService.enableBiometric(email, user.id);
          console.log('Biometric authentication enabled successfully');
        } catch (biometricError) {
          console.warn('Failed to enable biometric authentication:', biometricError);
          // Don't fail the login if biometric setup fails
        }
      }

      return user;
    } catch (error) {
      console.error('Complete login error:', error);
      throw error;
    }
  }

  // Register new user
  async register(
    email: string,
    password: string,
    fullName: string,
    phone: string
  ): Promise<User> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            phone: phone,
          },
        },
      });

      if (error) throw error;
      if (!data.user) throw new Error('No user data received');

      return data.user as User;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;
      return user as User | null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Get user profile with roles
  async getUserProfile(): Promise<UserProfile | null> {
    try {
      const user = await this.getCurrentUser();
      if (!user) return null;

      // Fetch user profile with roles from your API
      const profile = await apiClient.get<UserProfile>(`/api/users/${user.id}/profile`);
      return profile;
    } catch (error) {
      console.error('Get user profile error:', error);
      return null;
    }
  }

  // Reset password
  async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${CONFIG.API_BASE_URL}/reset-password`,
      });

      if (error) throw error;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }

  // Update password
  async updatePassword(newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;
    } catch (error) {
      console.error('Update password error:', error);
      throw error;
    }
  }

  // Update profile
  async updateProfile(updates: Partial<User>): Promise<User> {
    try {
      const { data, error } = await supabase.auth.updateUser({
        data: updates,
      });

      if (error) throw error;
      if (!data.user) throw new Error('No user data received');

      return data.user as User;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange((event, session) => {
      callback(session?.user as User | null);
    });
  }

  // Biometric Authentication Methods

  // Check if biometric login is available and enabled
  async isBiometricLoginAvailable(): Promise<boolean> {
    try {
      const isAvailable = await biometricService.isBiometricAvailable();
      const isEnabled = await biometricService.isBiometricEnabled();
      const hasCredentials = await biometricService.hasBiometricCredentials();

      return isAvailable && isEnabled && hasCredentials;
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return false;
    }
  }

  // Login with biometric authentication
  async loginWithBiometric(): Promise<User> {
    try {
      // Check if biometric login is available
      const isAvailable = await this.isBiometricLoginAvailable();
      if (!isAvailable) {
        throw new Error('Biometric login is not available');
      }

      // Get stored credentials using biometric authentication
      const credentials = await biometricService.getBiometricCredentials();
      if (!credentials) {
        throw new Error('No biometric credentials found');
      }

      // For biometric login, we'll use a special API endpoint that validates the user ID
      // This is more secure than storing passwords
      const response = await apiClient.post<{ user: User; session: any }>('/api/auth/biometric-login', {
        email: credentials.email,
        userId: credentials.userId,
      });

      if (!response.user) {
        throw new Error('Biometric authentication failed');
      }

      // Set the session in Supabase if provided
      if (response.session) {
        await supabase.auth.setSession(response.session);
      }

      return response.user;
    } catch (error) {
      console.error('Biometric login error:', error);
      // Fallback: disable biometric if it fails consistently
      if (error.message?.includes('credentials') || error.message?.includes('authentication failed')) {
        await this.disableBiometricAuth();
      }
      throw error;
    }
  }

  // Enable biometric authentication for current user
  async enableBiometricAuth(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      if (!user || !user.email || !user.id) {
        throw new Error('No authenticated user found');
      }

      return await biometricService.enableBiometric(user.email, user.id);
    } catch (error) {
      console.error('Enable biometric auth error:', error);
      throw error;
    }
  }

  // Disable biometric authentication
  async disableBiometricAuth(): Promise<void> {
    try {
      await biometricService.disableBiometric();
    } catch (error) {
      console.error('Disable biometric auth error:', error);
      throw error;
    }
  }

  // Get biometric type for UI display
  async getBiometricType(): Promise<string | null> {
    try {
      return await biometricService.getBiometricType();
    } catch (error) {
      console.error('Get biometric type error:', error);
      return null;
    }
  }
}

export const authService = new AuthService();
export default authService;
