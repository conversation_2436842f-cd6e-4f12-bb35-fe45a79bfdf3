{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,231,322,400,470,540,624,694,770,846,929,1008,1087,1168,1242,1325,1407,1485,1561,1633,1721,1796,1872,1950", "endColumns": "73,101,90,77,69,69,83,69,75,75,82,78,78,80,73,82,81,77,75,71,87,74,75,77,76", "endOffsets": "124,226,317,395,465,535,619,689,765,841,924,1003,1082,1163,1237,1320,1402,1480,1556,1628,1716,1791,1867,1945,2022"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,62,64,65,66,67,68,69,70,71,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2809,3153,3255,3346,5693,5763,5833,5917,5987,6063,6139,6222,6425,6589,6670,6744,6827,6909,6987,7063,7135,7324,7399,7475,7553", "endColumns": "73,101,90,77,69,69,83,69,75,75,82,78,78,80,73,82,81,77,75,71,87,74,75,77,76", "endOffsets": "2878,3250,3341,3419,5758,5828,5912,5982,6058,6134,6217,6296,6499,6665,6739,6822,6904,6982,7058,7130,7218,7394,7470,7548,7625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,325,432,578,676,818,928,1038,1153,1282,1420,1554,1683,1818,1918,2072,2194,2333,2470,2594,2718,2814,2942,3029,3148,3247,3378", "endColumns": "150,118,106,145,97,141,109,109,114,128,137,133,128,134,99,153,121,138,136,123,123,95,127,86,118,98,130,99", "endOffsets": "201,320,427,573,671,813,923,1033,1148,1277,1415,1549,1678,1813,1913,2067,2189,2328,2465,2589,2713,2809,2937,3024,3143,3242,3373,3473"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,61,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2883,3034,3424,3531,3677,3775,3917,4027,4137,4252,4381,4519,4653,4782,4917,5017,5171,5293,5432,5569,6301,7630,7726,7854,7941,8060,8159,8290", "endColumns": "150,118,106,145,97,141,109,109,114,128,137,133,128,134,99,153,121,138,136,123,123,95,127,86,118,98,130,99", "endOffsets": "3029,3148,3526,3672,3770,3912,4022,4132,4247,4376,4514,4648,4777,4912,5012,5166,5288,5427,5564,5688,6420,7721,7849,7936,8055,8154,8285,8385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,6504", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,6584"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "72", "startColumns": "4", "startOffsets": "7223", "endColumns": "100", "endOffsets": "7319"}}]}]}