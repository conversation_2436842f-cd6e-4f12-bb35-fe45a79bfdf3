{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,334,444,594,685,826,939,1051,1169,1307,1450,1571,1704,1848,1948,2103,2230,2368,2513,2635,2759,2855,2979,3066,3185,3286,3415", "endColumns": "156,121,109,149,90,140,112,111,117,137,142,120,132,143,99,154,126,137,144,121,123,95,123,86,118,100,128,97", "endOffsets": "207,329,439,589,680,821,934,1046,1164,1302,1445,1566,1699,1843,1943,2098,2225,2363,2508,2630,2754,2850,2974,3061,3180,3281,3410,3508"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2850,3007,3423,3533,3683,3774,3915,4028,4140,4258,4396,4539,4660,4793,4937,5037,5192,5319,5457,5602,6477,7781,7877,8001,8088,8207,8308,8437", "endColumns": "156,121,109,149,90,140,112,111,117,137,142,120,132,143,99,154,126,137,144,121,123,95,123,86,118,100,128,97", "endOffsets": "3002,3124,3528,3678,3769,3910,4023,4135,4253,4391,4534,4655,4788,4932,5032,5187,5314,5452,5597,5719,6596,7872,7996,8083,8202,8303,8432,8530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,256,339,422,494,563,647,716,786,863,941,1024,1103,1175,1254,1333,1407,1491,1575,1654,1724,1794,1876,1951,2027,2099", "endColumns": "72,127,82,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "123,251,334,417,489,558,642,711,781,858,936,1019,1098,1170,1249,1328,1402,1486,1570,1649,1719,1789,1871,1946,2022,2094,2168"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2777,3129,3257,3340,5724,5796,5865,5949,6018,6088,6165,6243,6326,6405,6601,6761,6840,6914,6998,7082,7161,7231,7301,7484,7559,7635,7707", "endColumns": "72,127,82,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "2845,3252,3335,3418,5791,5860,5944,6013,6083,6160,6238,6321,6400,6472,6675,6835,6909,6993,7077,7156,7226,7296,7378,7554,7630,7702,7776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,6680", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,6756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7383", "endColumns": "100", "endOffsets": "7479"}}]}]}