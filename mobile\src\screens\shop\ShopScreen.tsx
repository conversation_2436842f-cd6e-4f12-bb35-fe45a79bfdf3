import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { COLORS } from '../../constants/theme';
import PartsListing from '../../components/PartsListing';

// Mock parts data for demo
const mockParts = [
  {
    id: 1,
    title: 'Volkswagen (VW) Audi Brake Pad Set 1K0698151 Compatible with Golf, A3, Passat',
    price: 'KSh 4,500',
    discountedPrice: 'KSh 5,200',
    condition: 'New',
    stock: 5,
    thumbnailUrl: null,
  },
  {
    id: 2,
    title: 'VW Golf Engine Oil Filter 06A115561B Compatible with Golf MK5, MK6',
    price: 'KSh 1,200',
    condition: 'New',
    stock: 12,
    thumbnailUrl: null,
  },
  {
    id: 3,
    title: 'Audi A3 Headlight Assembly 8P0941003 Compatible with A3 8P',
    price: 'KSh 15,000',
    condition: 'Used',
    stock: 2,
    thumbnailUrl: null,
  },
  {
    id: 4,
    title: 'VW Passat Timing Belt Kit 038198119A Compatible with Passat B5, B6',
    price: 'KSh 8,500',
    condition: 'New',
    stock: 8,
    thumbnailUrl: null,
  },
  {
    id: 5,
    title: 'Audi Q7 Air Suspension Compressor 7L8616007A',
    price: 'KSh 45,000',
    discountedPrice: 'KSh 52,000',
    condition: 'Refurbished',
    stock: 1,
    thumbnailUrl: null,
  },
  {
    id: 6,
    title: 'VW Touareg Brake Disc Set 7L6615301A Compatible with Touareg, Q7',
    price: 'KSh 12,000',
    condition: 'New',
    stock: 6,
    thumbnailUrl: null,
  },
  {
    id: 7,
    title: 'Audi A4 Xenon Headlight Ballast 8E0907391',
    price: 'KSh 18,500',
    condition: 'Used',
    stock: 3,
    thumbnailUrl: null,
  },
  {
    id: 8,
    title: 'VW Golf GTI Turbocharger 06A145704T',
    price: 'KSh 85,000',
    discountedPrice: 'KSh 95,000',
    condition: 'Refurbished',
    stock: 2,
    thumbnailUrl: null,
  },
];

export const ShopScreen: React.FC = () => {
  const [parts, setParts] = useState(mockParts);
  const [loading, setLoading] = useState(false);
  const handlePartPress = (partId: number) => {
    console.log('Part pressed:', partId);
    // TODO: Navigate to part details
  };

  const handleCallPress = (partId: number) => {
    console.log('Call pressed for part:', partId);
    // TODO: Implement call functionality
  };

  const handleWhatsAppPress = (partId: number) => {
    console.log('WhatsApp pressed for part:', partId);
    // TODO: Implement WhatsApp functionality
  };

  const handleRefresh = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setParts(mockParts);
      setLoading(false);
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <PartsListing
        parts={parts}
        onPartPress={handlePartPress}
        onCallPress={handleCallPress}
        onWhatsAppPress={handleWhatsAppPress}
        title="Shop"
        subtitle={`${parts.length} parts available`}
        loading={loading}
        onRefresh={handleRefresh}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
});
