import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Switch,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  Linking
} from 'react-native';
import { biometricAuthService } from '../services/BiometricAuthService';
import { BiometricErrorHandler } from '../components/BiometricErrorHandler';
import { BiometricErrorCodes } from '../types/biometric.types';
import { BIOMETRIC_CONSTANTS } from '../utils/biometric-constants';

interface SettingsScreenProps {
  userId: string;
  onNavigateBack?: () => void;
}

export const SettingsScreen: React.FC<SettingsScreenProps> = ({
  userId,
  onNavigateBack
}) => {
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometryType, setBiometryType] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [biometricError, setBiometricError] = useState<string>('');

  useEffect(() => {
    checkBiometricStatus();
  }, []);

  const checkBiometricStatus = async () => {
    try {
      const supportInfo = await biometricAuthService.checkBiometricSupport();
      setBiometricAvailable(supportInfo.isSupported);
      setBiometryType(supportInfo.biometryType || '');

      if (supportInfo.isSupported) {
        const keysExist = await biometricAuthService.doesBiometricKeyExist();
        setBiometricEnabled(keysExist);
      }
    } catch (error) {
      console.error('SettingsScreen: Error checking biometric status:', error);
    }
  };

  const handleBiometricToggle = async (enabled: boolean) => {
    if (enabled) {
      await enableBiometric();
    } else {
      await disableBiometric();
    }
  };

  const enableBiometric = async () => {
    setIsLoading(true);
    setBiometricError('');

    try {
      const result = await biometricAuthService.enrollBiometrics(userId);
      
      if (result.success) {
        setBiometricEnabled(true);
        Alert.alert(
          'Success!',
          BIOMETRIC_CONSTANTS.MESSAGES.ENROLLMENT_SUCCESS,
          [{ text: 'OK' }]
        );
      } else {
        setBiometricError(result.error || 'UNKNOWN_ERROR');
      }
    } catch (error) {
      console.error('SettingsScreen: Error enabling biometric:', error);
      setBiometricError(BiometricErrorCodes.ERROR_UNABLE_TO_PROCESS);
    } finally {
      setIsLoading(false);
    }
  };

  const disableBiometric = async () => {
    Alert.alert(
      'Disable Biometric Login',
      'Are you sure you want to disable biometric authentication? You will need to use your password to login.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Disable', style: 'destructive', onPress: confirmDisableBiometric }
      ]
    );
  };

  const confirmDisableBiometric = async () => {
    setIsLoading(true);

    try {
      const result = await biometricAuthService.disableBiometrics(userId);
      
      if (result.success) {
        setBiometricEnabled(false);
        Alert.alert(
          'Disabled',
          BIOMETRIC_CONSTANTS.MESSAGES.DISABLE_SUCCESS,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Error',
          result.error || BIOMETRIC_CONSTANTS.MESSAGES.DISABLE_FAILED,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('SettingsScreen: Error disabling biometric:', error);
      Alert.alert(
        'Error',
        BIOMETRIC_CONSTANTS.MESSAGES.DISABLE_FAILED,
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleBiometricRetry = () => {
    setBiometricError('');
    enableBiometric();
  };

  const handleOpenSettings = () => {
    setBiometricError('');
    Alert.alert(
      'Open Settings',
      'Please go to your device settings to set up biometric authentication',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => Linking.openSettings() }
      ]
    );
  };

  const handleReEnrollBiometric = () => {
    setBiometricError('');
    // Force re-enrollment by disabling and then enabling
    Alert.alert(
      'Re-enroll Biometric',
      'Your biometric data has changed. We need to set up biometric authentication again.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Re-enroll', 
          onPress: async () => {
            // First disable to clean up old keys
            await biometricAuthService.disableBiometrics(userId);
            setBiometricEnabled(false);
            // Then enable again
            enableBiometric();
          }
        }
      ]
    );
  };

  const getBiometricLabel = () => {
    if (biometryType.toLowerCase().includes('face')) {
      return 'Face ID Login';
    } else if (biometryType.toLowerCase().includes('touch')) {
      return 'Touch ID Login';
    } else {
      return 'Fingerprint Login';
    }
  };

  const getBiometricDescription = () => {
    if (biometryType.toLowerCase().includes('face')) {
      return 'Use Face ID to quickly and securely login to your account';
    } else if (biometryType.toLowerCase().includes('touch')) {
      return 'Use Touch ID to quickly and securely login to your account';
    } else {
      return 'Use your fingerprint to quickly and securely login to your account';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Security Settings</Text>
        {onNavigateBack && (
          <TouchableOpacity style={styles.backButton} onPress={onNavigateBack}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.content}>
        {/* Biometric Authentication Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Biometric Authentication</Text>
          
          {biometricAvailable ? (
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingLabel}>{getBiometricLabel()}</Text>
                <Text style={styles.settingDescription}>
                  {getBiometricDescription()}
                </Text>
              </View>
              
              <View style={styles.settingControl}>
                {isLoading ? (
                  <ActivityIndicator size="small" color="#007AFF" />
                ) : (
                  <Switch
                    value={biometricEnabled}
                    onValueChange={handleBiometricToggle}
                    trackColor={{ false: '#767577', true: '#81b0ff' }}
                    thumbColor={biometricEnabled ? '#007AFF' : '#f4f3f4'}
                    disabled={isLoading}
                  />
                )}
              </View>
            </View>
          ) : (
            <View style={styles.unavailableContainer}>
              <Text style={styles.unavailableText}>
                Biometric authentication is not available on this device
              </Text>
              <TouchableOpacity
                style={styles.settingsButton}
                onPress={() => Linking.openSettings()}
              >
                <Text style={styles.settingsButtonText}>Open Device Settings</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Security Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Security Information</Text>
          <View style={styles.infoContainer}>
            <Text style={styles.infoText}>
              • Biometric data is stored securely on your device and never sent to our servers
            </Text>
            <Text style={styles.infoText}>
              • If you change your biometric data (add/remove fingerprints), you'll need to re-enroll
            </Text>
            <Text style={styles.infoText}>
              • You can always use your password as a backup authentication method
            </Text>
          </View>
        </View>
      </View>

      {/* Biometric Error Handler */}
      <BiometricErrorHandler
        errorCode={biometricError}
        onRetry={handleBiometricRetry}
        onSettings={handleOpenSettings}
        onReEnroll={handleReEnrollBiometric}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a2332',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    color: '#007AFF',
    fontSize: 16,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a2332',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a2332',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  settingControl: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 50,
  },
  unavailableContainer: {
    alignItems: 'center',
    padding: 20,
  },
  unavailableText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  settingsButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  settingsButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  infoContainer: {
    paddingLeft: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
    marginBottom: 8,
  },
});

export default SettingsScreen;
