export const BIOMETRIC_CONSTANTS = {
  KEY_ALIAS: 'autoflow_biometric_key',
  PROMPT_TITLE: 'Biometric Authentication',
  PROMPT_SUBTITLE: 'Use your fingerprint to authenticate',
  PROMPT_DESCRIPTION: 'Place your finger on the sensor to continue',
  CANCEL_BUTTON: 'Cancel',
  FALLBACK_BUTTON: 'Use PIN/Password',
  
  // Error messages
  MESSAGES: {
    LOCKOUT: 'Too many failed attempts. Please try again later or use your device PIN/password.',
    NO_BIOMETRICS: 'No biometric data enrolled. Please set up fingerprint or face recognition in your device settings.',
    HW_UNAVAILABLE: 'Biometric hardware is temporarily unavailable. Please try again later.',
    HW_NOT_PRESENT: 'This device does not support biometric authentication.',
    KEY_INVALIDATED: 'Your biometric data has changed. For security, please re-enroll your biometric authentication.',
    TIMEOUT: 'Authentication timed out. Please try again.',
    GENERIC_ERROR: 'Biometric authentication failed. Please try again.',
    ROOT_DETECTED: 'Biometric authentication is disabled on rooted devices for security reasons.',
    ENROLLMENT_SUCCESS: 'Biometric authentication has been successfully enabled!',
    <PERSON>NR<PERSON>LMENT_FAILED: 'Failed to enable biometric authentication. Please try again.',
    DISABLE_SUCCESS: 'Biometric authentication has been disabled.',
    DISABLE_FAILED: 'Failed to disable biometric authentication.'
  }
} as const;
