'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { createClient } from '@/app/libs/supabase/client';
// We'll create our own header instead of importing DashboardHeader
import CategoryStats from './components/CategoryStats';
import AddCategoryModal from './components/AddCategoryModal';
import CategoryList from './components/CategoryList';
import { Plus, Search, RefreshCw } from 'lucide-react';
import { CategoryWithChildren } from './types';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

export default function CategoriesDashboard() {
  const [categories, setCategories] = useState<CategoryWithChildren[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<CategoryWithChildren[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  // Track expanded categories and subcategories
  const [expandedCategories, setExpandedCategories] = useState<Record<number, boolean>>({});
  const [expandedSubcategories, setExpandedSubcategories] = useState<Record<number, boolean>>({});
  const [stats, setStats] = useState({
    totalCategories: 0,
    activeCategories: 0,
    parentCategories: 0,
    childCategories: 0,
    maxDepth: 0,
    engineParts: 0
  });

  const supabase = createClient();

  // Fetch categories from Supabase
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('car_part_categories')
          .select('*')
          .order('label');

        if (error) throw error;

        // Process categories to create a hierarchical structure
        const categoriesWithChildren = processCategories(data || []);
        setCategories(categoriesWithChildren);
        setFilteredCategories(categoriesWithChildren);

        // Calculate stats
        const activeCount = data?.filter(cat => cat.isActive).length || 0;
        const parentCount = data?.filter(cat => !cat.parent_category_id).length || 0;
        const childCount = data?.filter(cat => cat.parent_category_id).length || 0;
        const enginePartsCount = data?.filter(cat => cat.isEnginePart).length || 0;

        // Calculate max depth of the category tree
        const calculateMaxDepth = (categories: CategoryWithChildren[]): number => {
          if (!categories || categories.length === 0) return 0;

          let maxDepth = 0;

          const getDepth = (category: CategoryWithChildren, currentDepth: number) => {
            if (currentDepth > maxDepth) {
              maxDepth = currentDepth;
            }

            if (category.children && category.children.length > 0) {
              category.children.forEach(child => getDepth(child, currentDepth + 1));
            }
          };

          categories.forEach(category => getDepth(category, 1));

          return maxDepth;
        };

        const maxDepth = calculateMaxDepth(categoriesWithChildren);

        setStats({
          totalCategories: data?.length || 0,
          activeCategories: activeCount,
          parentCategories: parentCount,
          childCategories: childCount,
          maxDepth,
          engineParts: enginePartsCount
        });
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, [refreshTrigger]);

  // Process categories to create a hierarchical structure
  const processCategories = (data: any[]): CategoryWithChildren[] => {
    // Create a map for quick lookup
    const categoryMap = new Map();
    data.forEach(category => {
      categoryMap.set(category.id, {
        ...category,
        children: []
      });
    });

    // Build the tree structure
    const rootCategories: CategoryWithChildren[] = [];
    data.forEach(category => {
      if (category.parent_category_id) {
        const parent = categoryMap.get(category.parent_category_id);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      } else {
        rootCategories.push(categoryMap.get(category.id));
      }
    });

    return rootCategories;
  };

  // Filter categories based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCategories(categories);
      return;
    }

    const query = searchQuery.toLowerCase();

    // Helper function to search through category and its children
    const searchCategory = (category: CategoryWithChildren): boolean => {
      if (category.label.toLowerCase().includes(query)) {
        return true;
      }

      if (category.children && category.children.length > 0) {
        return category.children.some(child => searchCategory(child));
      }

      return false;
    };

    const filtered = categories.filter(category => searchCategory(category));
    setFilteredCategories(filtered);
  }, [searchQuery, categories]);

  // Refresh categories
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Toggle expanded state for a category
  const toggleCategoryExpanded = (categoryId: number) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Toggle expanded state for a subcategory
  const toggleSubcategoryExpanded = (parentId: number, childId: number) => {
    setExpandedSubcategories(prev => {
      const updatedState = { ...prev };
      
      // Initialize the parent entry if it doesn't exist
      if (!updatedState[parentId]) {
        updatedState[parentId] = {};
      }
      
      // Toggle the specific child's expanded state
      updatedState[parentId][childId] = !updatedState[parentId]?.[childId];
      
      return updatedState;
    });
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Custom Header */}
      <div className="bg-white p-6 border-b border-gray-200 mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Categories Dashboard</h1>
        <p className="text-gray-600">Manage your car part categories</p>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Section */}
        <CategoryStats stats={stats} />

        {/* Search and Actions */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
          <div className="relative w-full md:w-96">
            <input
              type="text"
              placeholder="Search categories..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>

          <div className="flex gap-2 w-full md:w-auto">
            <button
              onClick={handleRefresh}
              className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              <RefreshCw size={18} />
              <span>Refresh</span>
            </button>

            <button
              onClick={() => setIsAddModalOpen(true)}
              className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            >
              <Plus size={18} />
              <span>Add Category</span>
            </button>
          </div>
        </div>

        {/* Categories Grid */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner size={40} />
          </div>
        ) : filteredCategories.length > 0 ? (
          <CategoryList
            categories={filteredCategories}
            onRefresh={handleRefresh}
            expandedCategories={expandedCategories}
            expandedSubcategories={expandedSubcategories}
            onToggleCategoryExpand={toggleCategoryExpanded}
            onToggleSubcategoryExpand={toggleSubcategoryExpanded}
          />
        ) : (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <h3 className="text-xl font-semibold text-gray-700 mb-2">No categories found</h3>
            <p className="text-gray-500 mb-4">
              {searchQuery ? 'No categories match your search criteria.' : 'Start by adding a new category.'}
            </p>
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            >
              <Plus size={18} />
              <span>Add Category</span>
            </button>
          </div>
        )}
      </div>

      {/* Add Category Modal */}
      <AddCategoryModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={handleRefresh}
        categories={categories}
      />
    </div>
  );
}
