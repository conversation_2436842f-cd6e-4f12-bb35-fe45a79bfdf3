{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,6568", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,6645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7276", "endColumns": "100", "endOffsets": "7372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,224,309,386,458,527,608,676,742,816,891,972,1053,1122,1201,1279,1353,1435,1516,1595,1668,1739,1827,1898,1974,2046", "endColumns": "68,99,84,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "119,219,304,381,453,522,603,671,737,811,886,967,1048,1117,1196,1274,1348,1430,1511,1590,1663,1734,1822,1893,1969,2041,2117"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2795,3135,3235,3320,5630,5702,5771,5852,5920,5986,6060,6135,6216,6297,6489,6650,6728,6802,6884,6965,7044,7117,7188,7377,7448,7524,7596", "endColumns": "68,99,84,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "2859,3230,3315,3392,5697,5766,5847,5915,5981,6055,6130,6211,6292,6361,6563,6723,6797,6879,6960,7039,7112,7183,7271,7443,7519,7591,7667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,326,438,590,678,822,937,1046,1163,1291,1414,1558,1675,1799,1896,2046,2172,2306,2444,2559,2682,2791,2927,3016,3132,3233,3361", "endColumns": "148,121,111,151,87,143,114,108,116,127,122,143,116,123,96,149,125,133,137,114,122,108,135,88,115,100,127,104", "endOffsets": "199,321,433,585,673,817,932,1041,1158,1286,1409,1553,1670,1794,1891,2041,2167,2301,2439,2554,2677,2786,2922,3011,3127,3228,3356,3461"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2864,3013,3397,3509,3661,3749,3893,4008,4117,4234,4362,4485,4629,4746,4870,4967,5117,5243,5377,5515,6366,7672,7781,7917,8006,8122,8223,8351", "endColumns": "148,121,111,151,87,143,114,108,116,127,122,143,116,123,96,149,125,133,137,114,122,108,135,88,115,100,127,104", "endOffsets": "3008,3130,3504,3656,3744,3888,4003,4112,4229,4357,4480,4624,4741,4865,4962,5112,5238,5372,5510,5625,6484,7776,7912,8001,8117,8218,8346,8451"}}]}]}