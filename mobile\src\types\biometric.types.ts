export interface BiometricSupportInfo {
  isSupported: boolean;
  biometryType: string | null;
  error?: string;
}

export interface BiometricEnrollmentResult {
  success: boolean;
  publicKey?: string;
  error?: string;
}

export interface BiometricAuthResult {
  success: boolean;
  token?: string;
  error?: string;
  errorCode?: string;
}

export interface ChallengeResponse {
  challenge: string;
  expiresAt: number;
}

export interface VerificationRequest {
  userId: string;
  challenge: string;
  signature: string;
}

export interface VerificationResponse {
  success: boolean;
  token?: string;
  error?: string;
}

export enum BiometricErrorCodes {
  ERROR_LOCKOUT = 'ERROR_LOCKOUT',
  ERROR_NO_BIOMETRICS = 'ERROR_NO_BIOMETRICS',
  ERROR_HW_UNAVAILABLE = 'ERROR_HW_UNAVAILABLE',
  ERROR_HW_NOT_PRESENT = 'ERROR_HW_NOT_PRESENT',
  ERROR_USER_CANCELED = 'ERROR_USER_CANCELED',
  ERROR_KEY_PERMANENTLY_INVALIDATED = 'ERROR_KEY_PERMANENTLY_INVALIDATED',
  ERROR_TIMEOUT = 'ERROR_TIMEOUT',
  ERROR_UNABLE_TO_PROCESS = 'ERROR_UNABLE_TO_PROCESS',
  ERROR_VENDOR = 'ERROR_VENDOR',
  ERROR_NEGATIVE_BUTTON = 'ERROR_NEGATIVE_BUTTON',
  ERROR_NO_DEVICE_CREDENTIAL = 'ERROR_NO_DEVICE_CREDENTIAL'
}
