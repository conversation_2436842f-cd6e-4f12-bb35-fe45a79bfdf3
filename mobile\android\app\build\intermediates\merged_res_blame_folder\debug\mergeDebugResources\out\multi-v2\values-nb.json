{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7304", "endColumns": "100", "endOffsets": "7400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,201,314,420,582,670,811,919,1036,1158,1302,1437,1573,1700,1841,1941,2096,2218,2359,2503,2630,2753,2845,2970,3057,3177,3278,3412", "endColumns": "145,112,105,161,87,140,107,116,121,143,134,135,126,140,99,154,121,140,143,126,122,91,124,86,119,100,133,96", "endOffsets": "196,309,415,577,665,806,914,1031,1153,1297,1432,1568,1695,1836,1936,2091,2213,2354,2498,2625,2748,2840,2965,3052,3172,3273,3407,3504"}, "to": {"startLines": "30,31,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,63,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2807,2953,3340,3446,3608,3696,3837,3945,4062,4184,4328,4463,4599,4726,4867,4967,5122,5244,5385,5529,6403,7704,7796,7921,8008,8128,8229,8363", "endColumns": "145,112,105,161,87,140,107,116,121,143,134,135,126,140,99,154,121,140,143,126,122,91,124,86,119,100,133,96", "endOffsets": "2948,3061,3441,3603,3691,3832,3940,4057,4179,4323,4458,4594,4721,4862,4962,5117,5239,5380,5524,5651,6521,7791,7916,8003,8123,8224,8358,8455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,6604", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,6679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,230,316,399,474,543,625,693,760,834,912,997,1077,1146,1224,1307,1383,1463,1543,1620,1690,1759,1844,1920,1995,2065", "endColumns": "69,104,85,82,74,68,81,67,66,73,77,84,79,68,77,82,75,79,79,76,69,68,84,75,74,69,77", "endOffsets": "120,225,311,394,469,538,620,688,755,829,907,992,1072,1141,1219,1302,1378,1458,1538,1615,1685,1754,1839,1915,1990,2060,2138"}, "to": {"startLines": "29,32,33,34,53,54,55,56,57,58,59,60,61,62,64,66,67,68,69,70,71,72,73,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2737,3066,3171,3257,5656,5731,5800,5882,5950,6017,6091,6169,6254,6334,6526,6684,6767,6843,6923,7003,7080,7150,7219,7405,7481,7556,7626", "endColumns": "69,104,85,82,74,68,81,67,66,73,77,84,79,68,77,82,75,79,79,76,69,68,84,75,74,69,77", "endOffsets": "2802,3166,3252,3335,5726,5795,5877,5945,6012,6086,6164,6249,6329,6398,6599,6762,6838,6918,6998,7075,7145,7214,7299,7476,7551,7621,7699"}}]}]}