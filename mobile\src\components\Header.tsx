import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Modal } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY, SHADOWS } from '../constants/theme';

interface HeaderProps {
  onMenuPress: () => void;
  onSearch: (query: string) => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuPress, onSearch }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = () => {
    if (searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  return (
    <View style={styles.container}>
      {/* Top Bar */}
      <View style={styles.topBar}>
        {/* Hamburger Menu */}
        <TouchableOpacity style={styles.menuButton} onPress={onMenuPress}>
          <View style={styles.hamburgerLine} />
          <View style={styles.hamburgerLine} />
          <View style={styles.hamburgerLine} />
        </TouchableOpacity>

        {/* Logo */}
        <Text style={styles.logo}>AutoFlow</Text>

        {/* Categories Button */}
        <TouchableOpacity style={styles.categoriesButton}>
          <View style={styles.gridIcon}>
            <View style={styles.gridDot} />
            <View style={styles.gridDot} />
            <View style={styles.gridDot} />
            <View style={styles.gridDot} />
          </View>
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <TextInput
            style={styles.searchInput}
            placeholder="What can we help you find?"
            placeholderTextColor={COLORS.TEXT_SECONDARY}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
          <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
            <Text style={styles.searchIcon}>🔍</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.BACKGROUND,
    paddingTop: 40, // Status bar padding
    ...SHADOWS.sm,
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
  },
  menuButton: {
    padding: SPACING.sm,
    borderRadius: 20,
  },
  hamburgerLine: {
    width: 20,
    height: 2,
    backgroundColor: COLORS.TEXT_PRIMARY,
    marginVertical: 2,
  },
  logo: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.TEXT_PRIMARY,
    letterSpacing: -0.5,
  },
  categoriesButton: {
    padding: SPACING.sm,
    borderRadius: 20,
  },
  gridIcon: {
    width: 20,
    height: 20,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gridDot: {
    width: 8,
    height: 8,
    backgroundColor: COLORS.TEXT_PRIMARY,
    borderRadius: 2,
    marginBottom: 2,
  },
  searchContainer: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.md,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.SURFACE,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.sm + 2,
    paddingHorizontal: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_PRIMARY,
  },
  searchButton: {
    padding: SPACING.sm + 2,
    paddingRight: SPACING.md,
  },
  searchIcon: {
    fontSize: TYPOGRAPHY.sizes.md,
  },
});

export default Header;
