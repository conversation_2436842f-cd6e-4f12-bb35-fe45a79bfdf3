// Demo storage service with simulated persistence
// For demo purposes, this simulates persistent storage behavior

// Use a global variable that simulates persistence during the demo session
// In a real app, this would be replaced with proper AsyncStorage or SQLite
let demoStorage: { [key: string]: string } = {};

// Simulate loading from "persistent" storage on app start
let isInitialized = false;

// Demo session state - simulates that once a user enables biometric, it persists
let demoSessionState = {
  hasEverLoggedIn: false,
  hasEverEnabledBiometric: false,
  userEmail: '<EMAIL>'
};

class SimpleStorageService {
  constructor() {
    console.log('Storage: Initialized demo storage service');
  }

  private async initialize(): Promise<void> {
    if (isInitialized) return;

    // Simulate loading from persistent storage
    // For demo purposes, we'll pre-populate with some "persistent" data
    console.log('Storage: Initializing demo storage...');

    // This simulates that certain data persists across app sessions
    // In a real app, this would load from actual persistent storage
    isInitialized = true;
    console.log('Storage: Demo storage initialized');
  }

  async setItem(key: string, value: string): Promise<void> {
    await this.initialize();
    try {
      demoStorage[key] = value;
      console.log(`Storage: Set ${key} = ${value} (demo persistent)`);

      // Update demo session state to simulate persistence
      if (key === 'user_logged_in' && value === 'true') {
        demoSessionState.hasEverLoggedIn = true;
        console.log('Storage: Demo session - user has logged in');
      }

      if (key === 'autoflow_biometric_enabled' && value === 'true') {
        demoSessionState.hasEverEnabledBiometric = true;
        console.log('Storage: Demo session - biometric has been enabled');
      }

      if (key === 'user_email') {
        demoSessionState.userEmail = value;
        console.log('Storage: Demo session - user email saved');
      }

      // For demo purposes, simulate that login and biometric data persists
      if (this.isPersistentKey(key)) {
        console.log(`Storage: ${key} marked as persistent for demo`);
      }
    } catch (error) {
      console.error(`Storage: Error setting ${key}:`, error);
      throw error;
    }
  }

  async getItem(key: string): Promise<string | null> {
    await this.initialize();
    try {
      // For demo purposes, simulate that certain keys have persistent values
      if (this.isPersistentKey(key) && !(key in demoStorage)) {
        const persistentValue = this.getSimulatedPersistentValue(key);
        if (persistentValue) {
          demoStorage[key] = persistentValue;
          console.log(`Storage: Loaded simulated persistent value for ${key} = ${persistentValue}`);
        }
      }

      const value = demoStorage[key] || null;
      console.log(`Storage: Get ${key} = ${value} (demo storage)`);
      return value;
    } catch (error) {
      console.error(`Storage: Error getting ${key}:`, error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    await this.initialize();
    try {
      delete demoStorage[key];
      console.log(`Storage: Removed ${key} (demo storage)`);
    } catch (error) {
      console.error(`Storage: Error removing ${key}:`, error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    await this.initialize();
    try {
      demoStorage = {};
      console.log('Storage: Cleared all demo storage data');
    } catch (error) {
      console.error('Storage: Error clearing data:', error);
      throw error;
    }
  }

  // Get all keys
  async getAllKeys(): Promise<string[]> {
    await this.initialize();
    try {
      const keys = Object.keys(demoStorage);
      console.log('Storage: All keys from demo storage:', keys);
      return keys;
    } catch (error) {
      console.error('Storage: Error getting all keys:', error);
      return [];
    }
  }

  // Check if key exists
  async hasItem(key: string): Promise<boolean> {
    await this.initialize();
    try {
      const exists = key in demoStorage;
      console.log(`Storage: ${key} exists in demo storage: ${exists}`);
      return exists;
    } catch (error) {
      console.error(`Storage: Error checking if ${key} exists:`, error);
      return false;
    }
  }

  // Helper to determine if a key should simulate persistence
  private isPersistentKey(key: string): boolean {
    const persistentKeys = [
      'user_logged_in',
      'user_email',
      'autoflow_biometric_enabled',
      'autoflow_biometric_credentials'
    ];
    return persistentKeys.includes(key);
  }

  // Simulate persistent values for demo
  private getSimulatedPersistentValue(key: string): string | null {
    // For demo purposes, once the user has logged in and enabled biometric,
    // we simulate that this state persists across app restarts using session state

    console.log('Storage: Checking simulated persistence for', key, 'Session state:', demoSessionState);

    // If the user has ever logged in during this demo session, simulate persistence
    if (demoSessionState.hasEverLoggedIn) {
      if (key === 'user_logged_in') {
        console.log('Storage: Simulating persistent login state');
        return 'true';
      }

      if (key === 'user_email') {
        console.log('Storage: Simulating persistent user email');
        return demoSessionState.userEmail;
      }
    }

    // If the user has ever enabled biometric during this demo session, simulate persistence
    if (demoSessionState.hasEverEnabledBiometric) {
      if (key === 'autoflow_biometric_enabled') {
        console.log('Storage: Simulating persistent biometric enabled state');
        return 'true';
      }

      if (key === 'autoflow_biometric_credentials') {
        console.log('Storage: Simulating persistent biometric credentials');
        return JSON.stringify({
          email: demoSessionState.userEmail,
          userId: 'demo_user_id'
        });
      }
    }

    return null;
  }
}

export const simpleStorage = new SimpleStorageService();
export default simpleStorage;
