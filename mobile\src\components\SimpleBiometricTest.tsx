import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView
} from 'react-native';

// Direct import to test the library
import ReactNativeBiometrics from '@sbaiahmed1/react-native-biometrics';

const SimpleBiometricTest: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log('SimpleBiometricTest:', logMessage);
    setLogs(prev => [...prev, logMessage]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const testBasicBiometric = async () => {
    addLog('🔍 Starting basic biometric test...');
    
    try {
      // Create biometric instance
      const rnBiometrics = new ReactNativeBiometrics({
        allowDeviceCredentials: true
      });
      addLog('✅ Biometric instance created');

      // Check if sensor is available
      addLog('📱 Checking sensor availability...');
      const { available, biometryType, error } = await rnBiometrics.isSensorAvailable();
      
      addLog(`📊 Sensor available: ${available}`);
      addLog(`📊 Biometry type: ${biometryType || 'None'}`);
      
      if (error) {
        addLog(`❌ Sensor error: ${error}`);
        return;
      }

      if (!available) {
        addLog('❌ Biometric sensor not available on this device');
        Alert.alert('Not Available', 'Biometric authentication is not available on this device');
        return;
      }

      // Try to show biometric prompt
      addLog('🔐 Attempting to show biometric prompt...');
      
      const promptResult = await rnBiometrics.simplePrompt({
        promptMessage: 'Please authenticate with your biometric',
        cancelButtonText: 'Cancel'
      });

      addLog(`📊 Prompt result: ${JSON.stringify(promptResult)}`);

      if (promptResult.success) {
        addLog('🎉 Biometric authentication successful!');
        Alert.alert('Success!', 'Biometric authentication worked!');
      } else {
        addLog(`❌ Biometric authentication failed: ${promptResult.error}`);
        Alert.alert('Failed', `Authentication failed: ${promptResult.error}`);
      }

    } catch (error) {
      addLog(`💥 Exception occurred: ${error}`);
      Alert.alert('Error', `An error occurred: ${error}`);
    }
  };

  const testDeviceCapabilities = async () => {
    addLog('🔍 Testing device capabilities...');

    try {
      const rnBiometrics = new ReactNativeBiometrics();

      // Get detailed sensor info
      const sensorInfo = await rnBiometrics.isSensorAvailable();
      addLog(`📊 Full sensor info: ${JSON.stringify(sensorInfo, null, 2)}`);

      // Check if keys exist
      const keyInfo = await rnBiometrics.biometricKeysExist();
      addLog(`🔑 Keys exist: ${keyInfo.keysExist}`);

    } catch (error) {
      addLog(`💥 Device capabilities error: ${error}`);
    }
  };

  const testKeyManagement = async () => {
    addLog('🔑 Testing key management...');

    try {
      const rnBiometrics = new ReactNativeBiometrics();

      // Check if keys exist
      const { keysExist } = await rnBiometrics.biometricKeysExist();
      addLog(`🔑 Keys exist before: ${keysExist}`);

      if (keysExist) {
        // Delete existing keys
        addLog('🗑️ Deleting existing keys...');
        const deleteResult = await rnBiometrics.deleteKeys();
        addLog(`🗑️ Delete result: ${JSON.stringify(deleteResult)}`);

        // Check again
        const { keysExist: keysAfterDelete } = await rnBiometrics.biometricKeysExist();
        addLog(`🔑 Keys exist after delete: ${keysAfterDelete}`);
      }

      // Try to create new keys
      addLog('🔧 Creating new keys...');
      const { publicKey } = await rnBiometrics.createKeys();
      addLog(`✅ Keys created! Public key length: ${publicKey?.length || 0}`);

      // Verify keys exist
      const { keysExist: finalKeysExist } = await rnBiometrics.biometricKeysExist();
      addLog(`🔑 Final keys exist: ${finalKeysExist}`);

    } catch (error) {
      addLog(`💥 Key management error: ${error}`);
    }
  };

  const testPermissions = () => {
    addLog('🔍 Testing permissions...');
    addLog('📋 Required permissions:');
    addLog('  - android.permission.USE_FINGERPRINT');
    addLog('  - android.permission.USE_BIOMETRIC');
    addLog('  - android.permission.USE_FACE_ID');
    addLog('📋 Required features:');
    addLog('  - android.hardware.fingerprint');
    addLog('  - android.hardware.biometrics');
    addLog('✅ All permissions should be declared in AndroidManifest.xml');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🧪 Simple Biometric Test</Text>
      <Text style={styles.subtitle}>Direct library testing</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton]} 
          onPress={testBasicBiometric}
        >
          <Text style={styles.buttonText}>🔐 Test Biometric</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]} 
          onPress={testDeviceCapabilities}
        >
          <Text style={styles.buttonText}>📱 Device Info</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.button, styles.infoButton]}
          onPress={testPermissions}
        >
          <Text style={styles.buttonText}>🔒 Permissions</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.warningButton]}
          onPress={testKeyManagement}
        >
          <Text style={styles.buttonText}>🔑 Reset Keys</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearLogs}
        >
          <Text style={styles.buttonText}>🗑️ Clear</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.logsContainer}>
        <Text style={styles.logsTitle}>📝 Test Logs:</Text>
        {logs.length === 0 ? (
          <Text style={styles.noLogsText}>No logs yet. Run a test to see results.</Text>
        ) : (
          logs.map((log, index) => (
            <Text key={index} style={styles.logText}>
              {log}
            </Text>
          ))
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f8f9fa',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#2c3e50',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    color: '#7f8c8d',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    minWidth: '48%',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  primaryButton: {
    backgroundColor: '#3498db',
  },
  secondaryButton: {
    backgroundColor: '#2ecc71',
  },
  infoButton: {
    backgroundColor: '#f39c12',
  },
  warningButton: {
    backgroundColor: '#e67e22',
  },
  clearButton: {
    backgroundColor: '#e74c3c',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  logsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    maxHeight: 400,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  logsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#2c3e50',
  },
  noLogsText: {
    fontSize: 14,
    color: '#95a5a6',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
  logText: {
    fontSize: 13,
    marginBottom: 6,
    color: '#34495e',
    fontFamily: 'monospace',
    lineHeight: 18,
  },
});

export default SimpleBiometricTest;
