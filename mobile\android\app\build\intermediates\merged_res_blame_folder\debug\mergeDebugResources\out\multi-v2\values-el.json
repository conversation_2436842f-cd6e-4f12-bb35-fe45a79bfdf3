{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-41:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,6895", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,6976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,211,334,451,621,712,870,992,1120,1240,1392,1545,1685,1823,1976,2079,2256,2397,2540,2687,2820,2953,3049,3181,3269,3393,3503,3649", "endColumns": "155,122,116,169,90,157,121,127,119,151,152,139,137,152,102,176,140,142,146,132,132,95,131,87,123,109,145,104", "endOffsets": "206,329,446,616,707,865,987,1115,1235,1387,1540,1680,1818,1971,2074,2251,2392,2535,2682,2815,2948,3044,3176,3264,3388,3498,3644,3749"}, "to": {"startLines": "30,31,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,62,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2960,3116,3417,3534,3704,3795,3953,4075,4203,4323,4475,4628,4768,4906,5059,5162,5339,5480,5623,5770,6680,8038,8134,8266,8354,8478,8588,8734", "endColumns": "155,122,116,169,90,157,121,127,119,151,152,139,137,152,102,176,140,142,146,132,132,95,131,87,123,109,145,104", "endOffsets": "3111,3234,3529,3699,3790,3948,4070,4198,4318,4470,4623,4763,4901,5054,5157,5334,5475,5618,5765,5898,6808,8129,8261,8349,8473,8583,8729,8834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "7629", "endColumns": "100", "endOffsets": "7725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,221,307,383,453,537,609,677,755,836,920,1012,1084,1166,1253,1337,1422,1505,1585,1656,1726,1814,1886,1966,2040", "endColumns": "73,91,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "124,216,302,378,448,532,604,672,750,831,915,1007,1079,1161,1248,1332,1417,1500,1580,1651,1721,1809,1881,1961,2035,2117"}, "to": {"startLines": "29,32,33,52,53,54,55,56,57,58,59,60,61,63,65,66,67,68,69,70,71,72,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2886,3239,3331,5903,5979,6049,6133,6205,6273,6351,6432,6516,6608,6813,6981,7068,7152,7237,7320,7400,7471,7541,7730,7802,7882,7956", "endColumns": "73,91,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "2955,3326,3412,5974,6044,6128,6200,6268,6346,6427,6511,6603,6675,6890,7063,7147,7232,7315,7395,7466,7536,7624,7797,7877,7951,8033"}}]}]}